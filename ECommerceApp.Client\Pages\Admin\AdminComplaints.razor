@page "/admin/complaints"
@attribute [Authorize(Roles = "Admin")]
@inject ComplaintService ComplaintService
@inject ProductService ProductService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<PageTitle>Gestion des Réclamations - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Gestion des Réclamations</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-0"><i class="oi oi-comment-square me-2"></i>Gestion des Réclamations</h1>
        </div>
    </div>

    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="btn-group" role="group">
                <button type="button" class="btn @(filter == "all" ? "btn-primary" : "btn-outline-primary")" @onclick="ShowAll">
                    Toutes
                </button>
                <button type="button" class="btn @(filter == "open" ? "btn-primary" : "btn-outline-primary")" @onclick="ShowOpen">
                    Ouvertes
                </button>
                <button type="button" class="btn @(filter == "inprogress" ? "btn-primary" : "btn-outline-primary")" @onclick="ShowInProgress">
                    En cours
                </button>
                <button type="button" class="btn @(filter == "resolved" ? "btn-primary" : "btn-outline-primary")" @onclick="ShowResolved">
                    Résolues
                </button>
            </div>
        </div>
    </div>

@if (complaints == null)
{
    <div class="d-flex justify-content-center py-5">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else if (filteredComplaints.Count == 0)
{
    <div class="alert alert-info">
        <p class="mb-0">Aucune réclamation trouvée pour ce filtre.</p>
    </div>
}
else
{
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Sujet</th>
                            <th>Date</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var complaint in filteredComplaints)
                        {
                            <tr>
                                <td>@complaint.Id</td>
                                <td>@complaint.Username</td>
                                <td>@complaint.Subject</td>
                                <td>@complaint.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    @switch (complaint.Status)
                                    {
                                        case "Ouvert":
                                            <span class="badge bg-danger">@complaint.Status</span>
                                            break;
                                        case "En cours":
                                            <span class="badge bg-warning">@complaint.Status</span>
                                            break;
                                        case "Résolu":
                                            <span class="badge bg-success">@complaint.Status</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@complaint.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" @onclick="@(e => ShowComplaintDetails(complaint))">
                                        <i class="oi oi-eye me-1"></i> Répondre
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<!-- Modal de détails de réclamation -->
@if (selectedComplaint != null)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Réclamation #@selectedComplaint.Id</h5>
                    <button type="button" class="btn-close" @onclick="CloseModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Client</h6>
                            <p class="mb-0 fs-5">@selectedComplaint.Username</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Date de création</h6>
                            <p class="mb-0 fs-5">@selectedComplaint.CreatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Produit concerné</h6>
                            <p class="mb-0 fs-5">@(productName ?? "Chargement...")</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-1">Statut</h6>
                            <p class="mb-0">
                                @switch (selectedComplaint.Status)
                                {
                                    case "Ouvert":
                                        <span class="badge bg-danger">@selectedComplaint.Status</span>
                                        break;
                                    case "En cours":
                                        <span class="badge bg-warning">@selectedComplaint.Status</span>
                                        break;
                                    case "Résolu":
                                        <span class="badge bg-success">@selectedComplaint.Status</span>
                                        break;
                                    default:
                                        <span class="badge bg-secondary">@selectedComplaint.Status</span>
                                        break;
                                }
                            </p>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">@selectedComplaint.Subject</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">@selectedComplaint.Description</p>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(selectedComplaint.AdminResponse))
                    {
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Réponse de l'administrateur</h5>
                                <small>@selectedComplaint.ResponseDate?.ToString("dd/MM/yyyy HH:mm")</small>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">@selectedComplaint.AdminResponse</p>
                            </div>
                        </div>
                    }

                    <EditForm Model="responseModel" OnValidSubmit="SubmitResponse">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="mb-3">
                            <label for="status" class="form-label">Statut</label>
                            <InputSelect id="status" class="form-select" @bind-Value="responseModel.Status">
                                <option value="Ouvert">Ouvert</option>
                                <option value="En cours">En cours</option>
                                <option value="Résolu">Résolu</option>
                            </InputSelect>
                        </div>

                        <div class="mb-3">
                            <label for="adminResponse" class="form-label">Votre réponse</label>
                            <InputTextArea id="adminResponse" class="form-control" @bind-Value="responseModel.AdminResponse" rows="5" />
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" @onclick="CloseModal">Annuler</button>
                            <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                @if (isSubmitting)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span>Envoi en cours...</span>
                                }
                                else
                                {
                                    <span>Envoyer la réponse</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}
</div>

@code {
    private List<ComplaintDto> complaints;
    private List<ComplaintDto> filteredComplaints = new List<ComplaintDto>();
    private string filter = "all";
    private ComplaintDto selectedComplaint;
    private string productName;
    private bool isSubmitting = false;
    private ComplaintResponseDto responseModel = new ComplaintResponseDto();

    protected override async Task OnInitializedAsync()
    {
        await LoadComplaints();
    }

    private async Task LoadComplaints()
    {
        var response = await ComplaintService.GetComplaints();
        if (response.Success)
        {
            complaints = response.Data;
            ApplyFilter();
        }
        else
        {
            complaints = new List<ComplaintDto>();
            filteredComplaints = new List<ComplaintDto>();
        }
    }

    private void ShowAll()
    {
        filter = "all";
        ApplyFilter();
    }

    private void ShowOpen()
    {
        filter = "open";
        ApplyFilter();
    }

    private void ShowInProgress()
    {
        filter = "inprogress";
        ApplyFilter();
    }

    private void ShowResolved()
    {
        filter = "resolved";
        ApplyFilter();
    }

    private void ApplyFilter()
    {
        if (complaints == null)
        {
            filteredComplaints = new List<ComplaintDto>();
            return;
        }

        filteredComplaints = filter switch
        {
            "open" => complaints.Where(c => c.Status == "Ouvert").ToList(),
            "inprogress" => complaints.Where(c => c.Status == "En cours").ToList(),
            "resolved" => complaints.Where(c => c.Status == "Résolu").ToList(),
            _ => complaints.ToList()
        };
    }

    private async Task ShowComplaintDetails(ComplaintDto complaint)
    {
        selectedComplaint = complaint;
        responseModel = new ComplaintResponseDto
        {
            Status = complaint.Status,
            AdminResponse = complaint.AdminResponse ?? ""
        };

        // Charger les informations du produit
        try
        {
            var productResponse = await ProductService.GetProduct(complaint.ProductId);
            if (productResponse.Success && productResponse.Data != null)
            {
                productName = productResponse.Data.Name;
            }
            else
            {
                productName = "Produit non trouvé";
            }
        }
        catch
        {
            productName = "Aucun produit associé";
        }
    }

    private void CloseModal()
    {
        selectedComplaint = null;
        productName = null;
    }

    private async Task SubmitResponse()
    {
        if (selectedComplaint == null) return;

        isSubmitting = true;

        try
        {
            var updateModel = new UpdateComplaintDto
            {
                Status = responseModel.Status,
                AdminResponse = responseModel.AdminResponse
            };

            var response = await ComplaintService.RespondToComplaint(selectedComplaint.Id, updateModel);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Réponse envoyée avec succès !");
                await LoadComplaints();
                CloseModal();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    public class ComplaintResponseDto
    {
        public string Status { get; set; }
        public string AdminResponse { get; set; }
    }
}
