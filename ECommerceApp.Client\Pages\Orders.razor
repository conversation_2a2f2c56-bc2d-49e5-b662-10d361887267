@page "/orders"
@attribute [Authorize]
@inject OrderService OrderService
@inject NavigationManager NavigationManager

<PageTitle>Mes Commandes - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Mes Commandes</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-4"><i class="oi oi-list me-2"></i>Historique de mes commandes</h1>
            <p class="lead">Consultez l'historique de toutes vos commandes et accédez aux détails pour faire des réclamations sur des produits spécifiques.</p>
        </div>
    </div>

    @if (orders == null)
    {
        <div class="d-flex justify-content-center my-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (orders.Count == 0)
    {
        <div class="alert alert-info p-4 shadow-sm">
            <h4 class="alert-heading mb-3"><i class="oi oi-info me-2"></i>Aucune commande</h4>
            <p>Vous n'avez pas encore passé de commande.</p>
            <hr>
            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                <a href="/products" class="btn btn-primary">
                    <i class="oi oi-grid-three-up me-2"></i> Parcourir les produits
                </a>
                <a href="/cart" class="btn btn-outline-primary">
                    <i class="oi oi-cart me-2"></i> Voir mon panier
                </a>
            </div>
        </div>
    }
    else
    {
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="ps-4">N° Commande</th>
                                <th>Date</th>
                                <th>Adresse de livraison</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th class="text-end pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in orders)
                            {
                                <tr>
                                    <td class="ps-4 fw-bold">#@order.Id</td>
                                    <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="@order.ShippingAddress">
                                            @order.ShippingAddress
                                        </span>
                                    </td>
                                    <td class="fw-bold">@order.TotalAmount.ToString("C")</td>
                                    <td>
                                        @switch (order.Status)
                                        {
                                            case "En attente":
                                                <span class="badge bg-warning text-dark">
                                                    <i class="oi oi-clock me-1"></i> @order.Status
                                                </span>
                                                break;
                                            case "Expédiée":
                                                <span class="badge bg-info">
                                                    <i class="oi oi-box me-1"></i> @order.Status
                                                </span>
                                                break;
                                            case "Livrée":
                                                <span class="badge bg-success">
                                                    <i class="oi oi-check me-1"></i> @order.Status
                                                </span>
                                                break;
                                            case "Annulée":
                                                <span class="badge bg-danger">
                                                    <i class="oi oi-x me-1"></i> @order.Status
                                                </span>
                                                break;
                                            default:
                                                <span class="badge bg-secondary">
                                                    <i class="oi oi-question-mark me-1"></i> @order.Status
                                                </span>
                                                break;
                                        }
                                    </td>
                                    <td class="text-end pe-4">
                                        <button class="btn btn-sm btn-primary" @onclick="() => ViewOrderDetails(order.Id)">
                                            <i class="oi oi-eye me-1"></i> Détails
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<OrderDto> orders;

    protected override async Task OnInitializedAsync()
    {
        await LoadOrders();
    }

    private async Task LoadOrders()
    {
        var response = await OrderService.GetOrders();
        if (response.Success)
        {
            orders = response.Data;
        }
        else
        {
            orders = new List<OrderDto>();
        }
    }

    private void ViewOrderDetails(int orderId)
    {
        NavigationManager.NavigateTo($"/orders/{orderId}");
    }
}
