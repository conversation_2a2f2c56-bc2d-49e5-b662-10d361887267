using System;
using System.ComponentModel.DataAnnotations;

namespace ECommerceApp.Shared.Models
{
    public class Complaint
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int ProductId { get; set; }
        public string Subject { get; set; }
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string Status { get; set; } = "Ouvert"; 
        public string? AdminResponse { get; set; }
        public DateTime? ResponseDate { get; set; }

        
        public virtual User User { get; set; }
    }

    public class ComplaintDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int ProductId { get; set; }
        public string Username { get; set; }
        public string Subject { get; set; }
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Status { get; set; }
        public string? AdminResponse { get; set; }
        public DateTime? ResponseDate { get; set; }
    }

    public class CreateComplaintDto
    {
        [Required(ErrorMessage = "Le sujet est requis")]
        public string Subject { get; set; }

        [Required(ErrorMessage = "La description est requise")]
        [MinLength(10, ErrorMessage = "La description doit contenir au moins 10 caractères")]
        public string Description { get; set; }

        [Required(ErrorMessage = "Le produit est requis")]
        public int ProductId { get; set; }
    }

    public class UpdateComplaintDto
    {
        [Required(ErrorMessage = "Le statut est requis")]
        public string Status { get; set; }

        [Required(ErrorMessage = "La réponse est requise")]
        public string AdminResponse { get; set; }
    }
}
