using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;
using Microsoft.JSInterop;

namespace ECommerceApp.Client.Services
{
    public class HttpService
    {
        private readonly HttpClient _httpClient;
        private readonly IJSRuntime _jsRuntime;

        public HttpService(HttpClient httpClient, IJSRuntime jsRuntime)
        {
            _httpClient = httpClient;
            _jsRuntime = jsRuntime;
        }

        public async Task<ApiResponse<T>> Get<T>(string uri)
        {
            try
            {
                await SetAuthHeader();
                Console.WriteLine($"Sending GET request to {uri}");
                Console.WriteLine($"Base address: {_httpClient.BaseAddress}");

                // Vérifier si l'API est accessible - mais ne pas bloquer la requête principale
                try
                {
                    var testResponse = await _httpClient.GetAsync("api/products");
                    Console.WriteLine($"API test response: {testResponse.StatusCode}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API test failed: {ex.Message}");
                    // Ne pas retourner d'erreur ici, continuer avec la requête principale
                }

                var response = await _httpClient.GetAsync(uri);
                Console.WriteLine($"Response status: {response.StatusCode}");

                var result = await HandleResponse<T>(response);
                Console.WriteLine($"Response success: {result.Success}, Message: {result.Message}");
                return result;
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"HTTP request exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Erreur de connexion à l'API: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GET request: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Une erreur s'est produite: {ex.Message}");
            }
        }

        public async Task<ApiResponse<T>> Post<T>(string uri, object data)
        {
            try
            {
                await SetAuthHeader();

                // Utiliser des options de sérialisation pour gérer correctement les propriétés
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                };

                var jsonData = JsonSerializer.Serialize(data, options);
                Console.WriteLine($"Sending POST request to {uri} with data: {jsonData}");

                var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                Console.WriteLine($"Base address: {_httpClient.BaseAddress}");
                Console.WriteLine($"Content type: {content.Headers.ContentType}");

                // Vérifier si l'API est accessible - mais ne pas bloquer la requête principale
                try
                {
                    var testResponse = await _httpClient.GetAsync("api/products");
                    Console.WriteLine($"API test response: {testResponse.StatusCode}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API test failed: {ex.Message}");
                    // Ne pas retourner d'erreur ici, continuer avec la requête principale
                }

                // Afficher les en-têtes de la requête
                Console.WriteLine("Request headers:");
                foreach (var header in _httpClient.DefaultRequestHeaders)
                {
                    Console.WriteLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                var response = await _httpClient.PostAsync(uri, content);
                Console.WriteLine($"Response status: {response.StatusCode}");

                // Afficher les en-têtes de la réponse
                Console.WriteLine("Response headers:");
                foreach (var header in response.Headers)
                {
                    Console.WriteLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                var result = await HandleResponse<T>(response);
                Console.WriteLine($"Response success: {result.Success}, Message: {result.Message}");
                return result;
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"HTTP request exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Erreur de connexion à l'API: {ex.Message}");
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"JSON exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Erreur de sérialisation JSON: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in POST request: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Une erreur s'est produite: {ex.Message}");
            }
        }

        public async Task<ApiResponse<T>> Put<T>(string uri, object data)
        {
            try
            {
                await SetAuthHeader();
                var content = new StringContent(JsonSerializer.Serialize(data), Encoding.UTF8, "application/json");
                var response = await _httpClient.PutAsync(uri, content);
                return await HandleResponse<T>(response);
            }
            catch (Exception ex)
            {
                return ApiResponse<T>.ErrorResponse($"Une erreur s'est produite: {ex.Message}");
            }
        }

        public async Task<ApiResponse<T>> Delete<T>(string uri)
        {
            try
            {
                await SetAuthHeader();
                var response = await _httpClient.DeleteAsync(uri);
                return await HandleResponse<T>(response);
            }
            catch (Exception ex)
            {
                return ApiResponse<T>.ErrorResponse($"Une erreur s'est produite: {ex.Message}");
            }
        }

        private async Task<ApiResponse<T>> HandleResponse<T>(HttpResponseMessage response)
        {
            try
            {
                // Lire le contenu brut de la réponse pour le débogage
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Response content: {responseContent}");

                if (!response.IsSuccessStatusCode)
                {
                    try
                    {
                        var options = new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        };

                        var error = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, options);
                        if (error != null)
                        {
                            return error;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"Error deserializing error response: {ex.Message}");
                        Console.WriteLine($"Response content: {responseContent}");
                    }

                    return ApiResponse<T>.ErrorResponse($"Erreur HTTP: {response.StatusCode}. Contenu: {(responseContent.Length > 100 ? responseContent.Substring(0, 100) + "..." : responseContent)}");
                }

                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var apiResponse = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, options);
                    return apiResponse ?? ApiResponse<T>.ErrorResponse("Réponse invalide de l'API");
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"Error deserializing success response: {ex.Message}");
                    Console.WriteLine($"Response content: {responseContent}");
                    return ApiResponse<T>.ErrorResponse($"Erreur de désérialisation: {ex.Message}. Contenu: {(responseContent.Length > 100 ? responseContent.Substring(0, 100) + "..." : responseContent)}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error in HandleResponse: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return ApiResponse<T>.ErrorResponse($"Erreur inattendue: {ex.Message}");
            }
        }

        private async Task SetAuthHeader()
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }
    }
}
