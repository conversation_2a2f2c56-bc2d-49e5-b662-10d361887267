# E-Commerce App

Une application e-commerce complète avec un frontend Blazor WebAssembly et un backend API ASP.NET Core.

## Fonctionnalités

### Client
- Inscription et connexion
- Parcourir les produits
- Ajouter des produits au panier
- Passer des commandes
- Soumettre des réclamations
- Voir l'historique des commandes
- G<PERSON>rer son profil

### Admin
- Gérer les produits (ajouter, modifier, supprimer)
- Gérer les commandes (voir, mettre à jour le statut)
- Gérer les réclamations (voir, répondre)

## Structure du projet

- **ECommerceApp.API** : Backend API ASP.NET Core
- **ECommerceApp.Client** : Frontend Blazor WebAssembly
- **ECommerceApp.Shared** : Modèles partagés entre le frontend et le backend

## Prérequis

- .NET 9.0 SDK
- SQL Server (LocalDB est suffisant pour le développement)
- Visual Studio 2022 ou VS Code

## Installation et exécution

1. C<PERSON>z le dépôt
2. Ouvrez la solution dans Visual Studio ou VS Code
3. Exécutez les migrations de base de données :

```bash
cd ECommerceApp.API
dotnet ef migrations add InitialCreate
dotnet ef database update
```

4. Exécutez l'API :

```bash
cd ECommerceApp.API
dotnet run
```

5. Dans un autre terminal, exécutez le client :

```bash
cd ECommerceApp.Client
dotnet run
```

6. Accédez à l'application dans votre navigateur :
   - Client : https://localhost:7002
   - API Swagger : https://localhost:7001/swagger

## Comptes par défaut

- **Admin** :
  - Email : <EMAIL>
  - Mot de passe : admin123

## Technologies utilisées

- ASP.NET Core 9.0
- Blazor WebAssembly
- Entity Framework Core
- SQL Server
- JWT pour l'authentification
- Bootstrap 5 pour le design

## Licence

Ce projet est sous licence MIT.
