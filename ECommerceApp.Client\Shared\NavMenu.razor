@inject AuthService AuthService
@inject CartService CartService
@inject NavigationManager NavigationManager
@implements IDisposable

<nav class="navbar navbar-expand-lg navbar-dark shadow-sm" style="background-color: var(--primary-color);">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="/">
            <i class="bi bi-shop me-2" style="font-size: 1.5rem;"></i>
            <span class="fw-bold">E-Shop</span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/home"><i class="bi bi-house-fill me-1"></i> Accueil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="products"><i class="bi bi-grid-3x3-gap-fill me-1"></i> Produits</a>
                </li>

                @if (AuthService.IsAuthenticated)
                {
                    @if (AuthService.IsAdmin)
                    {
                        <!-- Menu Administration pour les administrateurs -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear-fill me-1"></i> Administration
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                <li><a class="dropdown-item" href="admin"><i class="bi bi-speedometer2 me-2"></i>Tableau de bord</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="admin/products"><i class="bi bi-box-seam-fill me-2"></i>Gestion des Produits</a></li>
                                <li><a class="dropdown-item" href="admin/complaints"><i class="bi bi-chat-square-text-fill me-2"></i>Gestion des Réclamations</a></li>
                                <li><a class="dropdown-item" href="admin/users"><i class="bi bi-people-fill me-2"></i>Gestion des Clients</a></li>
                            </ul>
                        </li>
                    }
                    else
                    {
                        <!-- Menu Client pour les utilisateurs normaux -->
                        <li class="nav-item">
                            <a class="nav-link position-relative" href="cart">
                                <i class="bi bi-cart-fill me-1"></i> Panier
                                @if (CartService.ItemCount > 0)
                                {
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-accent">
                                        @CartService.ItemCount
                                        <span class="visually-hidden">articles dans le panier</span>
                                    </span>
                                }
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="orders"><i class="bi bi-box-seam-fill me-1"></i> Mes Commandes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="complaints"><i class="bi bi-exclamation-triangle-fill me-1"></i> Mes Réclamations</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="complaint-messages"><i class="bi bi-envelope-fill me-1"></i> Messages de Réclamation</a>
                        </li>
                    }
                }
            </ul>
            <ul class="navbar-nav">
                @if (AuthService.IsAuthenticated)
                {
                    <!-- Utilisateur connecté -->
                    <li class="nav-item me-2">
                        <a class="nav-link" href="profile">
                            <i class="bi bi-person-circle me-1"></i> <span class="text-warning">@AuthService.CurrentUser.Username</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-light" @onclick="Logout">
                            <i class="bi bi-box-arrow-right me-1"></i> Déconnexion
                        </button>
                    </li>
                }
                else
                {
                    <!-- Utilisateur non connecté -->
                    <li class="nav-item">
                        <a class="btn btn-outline-light me-2" href="login">
                            <i class="bi bi-box-arrow-in-right me-1"></i> Connexion
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-light" href="register">
                            <i class="bi bi-person-plus-fill me-1"></i> Inscription
                        </a>
                    </li>
                }
            </ul>
        </div>
    </div>
</nav>

@code {
    protected override void OnInitialized()
    {
        Console.WriteLine("NavMenu OnInitialized");
        AuthService.OnAuthStateChanged += HandleAuthStateChanged;
        CartService.OnCartChanged += HandleCartChanged;
        Console.WriteLine("NavMenu event handlers registered");
        Console.WriteLine("Current auth state: " + (AuthService.IsAuthenticated ? "Authenticated" : "Not Authenticated"));
        if (AuthService.IsAuthenticated)
        {
            Console.WriteLine("Current user: " + AuthService.CurrentUser.Username);
        }
    }

    private void HandleAuthStateChanged()
    {
        Console.WriteLine("NavMenu: Auth state changed");
        Console.WriteLine("Is authenticated: " + AuthService.IsAuthenticated);
        if (AuthService.IsAuthenticated)
        {
            Console.WriteLine("Current user: " + AuthService.CurrentUser.Username);
        }
        StateHasChanged();
    }

    private void HandleCartChanged()
    {
        Console.WriteLine("NavMenu: Cart changed");
        StateHasChanged();
    }

    private async Task Logout()
    {
        await AuthService.Logout();
        NavigationManager.NavigateTo("/", true);
    }

    public void Dispose()
    {
        Console.WriteLine("NavMenu Dispose");
        AuthService.OnAuthStateChanged -= HandleAuthStateChanged;
        CartService.OnCartChanged -= HandleCartChanged;
        Console.WriteLine("NavMenu event handlers unregistered");
    }
}
