@page "/orders/{Id:int}"
@attribute [Authorize]
@inject OrderService OrderService
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Détail de la commande - E-Commerce App</PageTitle>

@if (order == null)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Commande #@order.Id</h1>
        <a href="/orders" class="btn btn-outline-primary">
            <i class="oi oi-arrow-left"></i> Retour aux commandes
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Informations de la commande</h5>
                </div>
                <div class="card-body">
                    <p><strong>Date:</strong> @order.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                    <p><strong>Client:</strong> @order.Username</p>
                    <p><strong>Statut:</strong>
                        @switch (order.Status)
                        {
                            case "En attente":
                                <span class="badge bg-warning text-dark">@order.Status</span>
                                break;
                            case "Expédiée":
                                <span class="badge bg-info">@order.Status</span>
                                break;
                            case "Livrée":
                                <span class="badge bg-success">@order.Status</span>
                                break;
                            case "Annulée":
                                <span class="badge bg-danger">@order.Status</span>
                                break;
                            default:
                                <span class="badge bg-secondary">@order.Status</span>
                                break;
                        }
                    </p>
                    <p><strong>Montant total:</strong> @order.TotalAmount.ToString("C")</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Adresse de livraison</h5>
                </div>
                <div class="card-body">
                    <p>@order.ShippingAddress</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Articles commandés</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th>Prix unitaire</th>
                            <th>Quantité</th>
                            <th>Sous-total</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in order.OrderItems)
                        {
                            <tr>
                                <td>@item.ProductName</td>
                                <td>@item.UnitPrice.ToString("C")</td>
                                <td>@item.Quantity</td>
                                <td>@item.Subtotal.ToString("C")</td>
                                <td>
                                    <a href="/complaints/create/@item.ProductId" class="btn btn-sm btn-outline-warning">
                                        <i class="oi oi-warning me-1"></i> Faire une réclamation
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Total:</strong></td>
                            <td><strong>@order.TotalAmount.ToString("C")</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    @if (AuthService.IsAdmin)
    {
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Mettre à jour le statut</h5>
            </div>
            <div class="card-body">
                <EditForm Model="@statusUpdateModel" OnValidSubmit="UpdateOrderStatus">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Nouveau statut</label>
                                <InputSelect id="status" class="form-select" @bind-Value="statusUpdateModel.Status">
                                    <option value="En attente">En attente</option>
                                    <option value="Expédiée">Expédiée</option>
                                    <option value="Livrée">Livrée</option>
                                    <option value="Annulée">Annulée</option>
                                </InputSelect>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary" disabled="@isProcessing">
                                @if (isProcessing)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span> Mise à jour...</span>
                                }
                                else
                                {
                                    <span>Mettre à jour</span>
                                }
                            </button>
                        </div>
                    </div>
                </EditForm>
            </div>
        </div>
    }
}

@code {
    [Parameter]
    public int Id { get; set; }

    private OrderDto order;
    private UpdateOrderStatusDto statusUpdateModel = new UpdateOrderStatusDto();
    private bool isProcessing = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadOrder();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadOrder();
    }

    private async Task LoadOrder()
    {
        var response = await OrderService.GetOrder(Id);
        if (response.Success)
        {
            order = response.Data;
            statusUpdateModel.Status = order.Status;
        }
        else
        {
            NavigationManager.NavigateTo("/orders");
            await JSRuntime.InvokeVoidAsync("alert", "Commande non trouvée.");
        }
    }

    private async Task UpdateOrderStatus()
    {
        if (!AuthService.IsAdmin)
        {
            return;
        }

        isProcessing = true;

        try
        {
            var response = await OrderService.UpdateOrderStatus(Id, statusUpdateModel);
            if (response.Success)
            {
                order = response.Data;
                await JSRuntime.InvokeVoidAsync("alert", "Statut de la commande mis à jour avec succès.");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Une erreur s'est produite: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }
}
