using ECommerceApp.Shared.Models;
using Microsoft.EntityFrameworkCore;

namespace ECommerceApp.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<Complaint> Complaints { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configuration des relations
            modelBuilder.Entity<Order>()
                .HasOne(o => o.User)
                .WithMany(u => u.Orders)
                .HasForeignKey(o => o.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Order)
                .WithMany(o => o.OrderItems)
                .HasForeignKey(oi => oi.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Product)
                .WithMany()
                .HasForeignKey(oi => oi.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Complaint>()
                .HasOne(c => c.User)
                .WithMany(u => u.Complaints)
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Données de démarrage
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Admin user
            modelBuilder.Entity<User>().HasData(
                new
                {
                    Id = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    Password = "admin123", // Dans un vrai projet, utilisez un hachage de mot de passe
                    Role = "Admin",
                    Address = (string)null,
                    PhoneNumber = (string)null
                }
            );

            // Produits de démarrage
            modelBuilder.Entity<Product>().HasData(
                new
                {
                    Id = 1,
                    Name = "Smartphone XYZ",
                    Description = "Un smartphone de dernière génération avec des fonctionnalités avancées.",
                    Price = 699.99m,
                    StockQuantity = 50,
                    ImageUrl = "/images/smartphone.jpg",
                    CreatedAt = new DateTime(2023, 1, 1),
                    UpdatedAt = (DateTime?)null
                },
                new
                {
                    Id = 2,
                    Name = "Ordinateur portable ABC",
                    Description = "Un ordinateur portable puissant pour les professionnels et les joueurs.",
                    Price = 1299.99m,
                    StockQuantity = 30,
                    ImageUrl = "/images/laptop.jpg",
                    CreatedAt = new DateTime(2023, 1, 1),
                    UpdatedAt = (DateTime?)null
                },
                new
                {
                    Id = 3,
                    Name = "Casque audio sans fil",
                    Description = "Un casque audio sans fil avec une qualité sonore exceptionnelle.",
                    Price = 199.99m,
                    StockQuantity = 100,
                    ImageUrl = "/images/headphones.jpg",
                    CreatedAt = new DateTime(2023, 1, 1),
                    UpdatedAt = (DateTime?)null
                }
            );
        }
    }
}
