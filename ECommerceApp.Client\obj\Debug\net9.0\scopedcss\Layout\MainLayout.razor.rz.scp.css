.page[b-j2hp4d7pob] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-j2hp4d7pob] {
    flex: 1;
}

.sidebar[b-j2hp4d7pob] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-j2hp4d7pob] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-j2hp4d7pob]  a, .top-row[b-j2hp4d7pob]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-j2hp4d7pob]  a:hover, .top-row[b-j2hp4d7pob]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-j2hp4d7pob]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-j2hp4d7pob] {
        justify-content: space-between;
    }

    .top-row[b-j2hp4d7pob]  a, .top-row[b-j2hp4d7pob]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-j2hp4d7pob] {
        flex-direction: row;
    }

    .sidebar[b-j2hp4d7pob] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-j2hp4d7pob] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-j2hp4d7pob]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-j2hp4d7pob], article[b-j2hp4d7pob] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
