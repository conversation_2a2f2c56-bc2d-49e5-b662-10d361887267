using System.Collections.Generic;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;

namespace ECommerceApp.Client.Services
{
    public class ComplaintService
    {
        private readonly HttpService _httpService;

        public ComplaintService(HttpService httpService)
        {
            _httpService = httpService;
        }

        public async Task<ApiResponse<List<ComplaintDto>>> GetComplaints()
        {
            return await _httpService.Get<List<ComplaintDto>>("api/complaints");
        }

        public async Task<ApiResponse<ComplaintDto>> GetComplaint(int id)
        {
            return await _httpService.Get<ComplaintDto>($"api/complaints/{id}");
        }

        public async Task<ApiResponse<ComplaintDto>> CreateComplaint(CreateComplaintDto model)
        {
            return await _httpService.Post<ComplaintDto>("api/complaints", model);
        }

        public async Task<ApiResponse<ComplaintDto>> RespondToComplaint(int id, UpdateComplaintDto model)
        {
            return await _httpService.Put<ComplaintDto>($"api/complaints/{id}/response", model);
        }

        public async Task<ApiResponse<ComplaintDto>> UpdateComplaint(int id, UpdateComplaintDto model)
        {
            return await _httpService.Put<ComplaintDto>($"api/complaints/{id}", model);
        }

        public async Task<ApiResponse<ComplaintDto>> UpdateComplaintStatus(int id, UpdateComplaintDto model)
        {
            return await _httpService.Put<ComplaintDto>($"api/complaints/{id}/status", model);
        }
    }
}
