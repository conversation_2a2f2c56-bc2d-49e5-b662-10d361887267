@page "/cart"
@inject CartService CartService
@inject OrderService OrderService
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Panier - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Panier</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-0"><i class="oi oi-basket me-2"></i>Mon <PERSON></h1>
        </div>
    </div>

    @if (!AuthService.IsAuthenticated)
    {
        <div class="card border-0 shadow-sm">
            <div class="card-body p-5 text-center">
                <div class="mb-4">
                    <i class="oi oi-lock-locked text-warning" style="font-size: 3rem;"></i>
                </div>
                <h3 class="mb-3">Vous devez être connecté pour accéder à votre panier</h3>
                <p class="text-muted mb-4">Connectez-vous pour voir votre panier et finaliser vos achats.</p>
                <div class="d-grid gap-2 col-md-6 mx-auto">
                    <a href="/login" class="btn btn-primary btn-lg">
                        <i class="oi oi-account-login me-2"></i> Se connecter
                    </a>
                    <a href="/register" class="btn btn-outline-primary">
                        <i class="oi oi-plus me-2"></i> Créer un compte
                    </a>
                </div>
            </div>
        </div>
    }
    else if (CartService.CartItems.Count == 0)
    {
        <div class="card border-0 shadow-sm">
            <div class="card-body p-5 text-center">
                <div class="mb-4">
                    <i class="oi oi-basket text-muted" style="font-size: 3rem;"></i>
                </div>
                <h3 class="mb-3">Votre panier est vide</h3>
                <p class="text-muted mb-4">Ajoutez des produits à votre panier pour commencer vos achats.</p>
                <a href="/products" class="btn btn-primary btn-lg">
                    <i class="oi oi-grid-three-up me-2"></i> Parcourir les produits
                </a>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Articles dans votre panier (@CartService.ItemCount)</h5>
                    </div>
                    <div class="card-body p-4">
                        @foreach (var item in CartService.CartItems)
                        {
                            <div class="row mb-4 border-bottom pb-4">
                                <div class="col-md-2 col-4">
                                    <div class="bg-image rounded">
                                        @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                        {
                                            <img src="@item.ProductImageUrl" alt="@item.ProductName" class="img-fluid rounded"
                                                 onerror="this.onerror=null; this.src='https://picsum.photos/150/150?random=@item.ProductId';">
                                        }
                                        else
                                        {
                                            <img src="https://picsum.photos/150/150?random=@item.ProductId" class="img-fluid rounded" alt="@item.ProductName">
                                        }
                                    </div>
                                </div>
                                <div class="col-md-10 col-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h5 class="mb-1">@item.ProductName</h5>
                                            <p class="text-muted mb-0 small">Prix unitaire: @item.UnitPrice.ToString("C")</p>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => RemoveFromCart(item.ProductId)">
                                                <i class="oi oi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div class="d-flex align-items-center">
                                            <div class="input-group input-group-sm" style="width: 120px;">
                                                <button class="btn btn-outline-secondary" @onclick="() => UpdateQuantity(item.ProductId, item.Quantity - 1)" disabled="@(item.Quantity <= 1)">
                                                    <i class="oi oi-minus"></i>
                                                </button>
                                                <input type="number" class="form-control text-center" value="@item.Quantity"
                                                       @onchange="@(e => UpdateQuantity(item.ProductId, e.Value != null ? int.Parse(e.Value.ToString()) : 1))" min="1">
                                                <button class="btn btn-outline-secondary" @onclick="() => UpdateQuantity(item.ProductId, item.Quantity + 1)">
                                                    <i class="oi oi-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="fw-bold">@item.Subtotal.ToString("C")</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <a href="/products" class="btn btn-outline-primary">
                                <i class="oi oi-arrow-left me-2"></i> Continuer les achats
                            </a>
                            <button class="btn btn-outline-danger" @onclick="ClearCart">
                                <i class="oi oi-trash me-2"></i> Vider le panier
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Résumé de la commande</h5>
                    </div>
                    <div class="card-body p-4">
                        <ul class="list-group list-group-flush mb-4">
                            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                                Sous-total
                                <span>@CartService.TotalPrice.ToString("C")</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">
                                Livraison
                                <span>Gratuite</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 mb-3">
                                <div>
                                    <strong>Total</strong>
                                    <p class="mb-0 text-muted small">(TVA incluse)</p>
                                </div>
                                <span class="fw-bold fs-5">@CartService.TotalPrice.ToString("C")</span>
                            </li>
                        </ul>

                        <button class="btn btn-primary btn-lg w-100" @onclick="ConfirmAndPlaceOrder" disabled="@isProcessing">
                            @if (isProcessing)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                <span>Traitement en cours...</span>
                            }
                            else
                            {
                                <i class="oi oi-credit-card me-2"></i>
                                <span>Passer la commande</span>
                            }
                        </button>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        <h5 class="mb-3">Besoin d'aide ?</h5>
                        <p class="text-muted small mb-0">
                            <i class="oi oi-phone me-2"></i> +216 71 123 456<br>
                            <i class="oi oi-envelope-closed me-2"></i> <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
        </div>


    }
</div>

@code {
    private bool isProcessing = false;
    private CreateOrderDto orderModel = new CreateOrderDto();

    protected override void OnInitialized()
    {
        if (AuthService.IsAuthenticated)
        {
            orderModel.ShippingAddress = AuthService.CurrentUser.Address;
        }
    }

    private async Task UpdateQuantity(int productId, int quantity)
    {
        await CartService.UpdateQuantity(productId, quantity);
    }

    private async Task RemoveFromCart(int productId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer ce produit du panier ?"))
        {
            await CartService.RemoveFromCart(productId);
        }
    }

    private async Task ClearCart()
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir vider votre panier ?"))
        {
            await CartService.ClearCart();
        }
    }

    private async Task ConfirmAndPlaceOrder()
    {
        // Vérifier si l'utilisateur est connecté
        if (!AuthService.IsAuthenticated)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Veuillez vous connecter pour passer une commande.");
            NavigationManager.NavigateTo("/login");
            return;
        }

        // Vérifier si le panier contient des articles
        if (CartService.CartItems.Count == 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Votre panier est vide. Veuillez ajouter des articles avant de passer commande.");
            return;
        }

        // Demander confirmation à l'utilisateur
        bool confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Voulez-vous confirmer votre commande de {CartService.ItemCount} article(s) pour un total de {CartService.TotalPrice.ToString("C")} ?\n\nLe paiement se fera à la livraison.");

        if (!confirmed)
        {
            return;
        }

        isProcessing = true;

        try
        {
            // Utiliser l'adresse de l'utilisateur connecté
            orderModel.ShippingAddress = AuthService.CurrentUser.Address;

            // Si l'adresse est vide, utiliser une adresse par défaut
            if (string.IsNullOrWhiteSpace(orderModel.ShippingAddress))
            {
                orderModel.ShippingAddress = "Adresse de livraison par défaut";
            }

            // Préparer la commande
            orderModel.OrderItems = CartService.CartItems.Select(item => new CreateOrderItemDto
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity
            }).ToList();

            Console.WriteLine($"Envoi de la commande avec {orderModel.OrderItems.Count} articles et adresse: {orderModel.ShippingAddress}");

            // Envoyer la commande
            var response = await OrderService.CreateOrder(orderModel);

            if (response.Success)
            {
                await CartService.ClearCart();
                await JSRuntime.InvokeVoidAsync("alert", "Votre commande a été passée avec succès ! Vous pouvez la consulter dans 'Mes Commandes'.");
                NavigationManager.NavigateTo($"/orders/{response.Data.Id}");
            }
            else
            {
                Console.WriteLine($"Erreur lors de la création de la commande: {response.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la création de la commande: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Une erreur s'est produite: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }
}
