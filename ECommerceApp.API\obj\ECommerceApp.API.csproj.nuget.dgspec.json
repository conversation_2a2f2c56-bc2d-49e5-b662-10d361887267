{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.API\\ECommerceApp.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.API\\ECommerceApp.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.API\\ECommerceApp.API.csproj", "projectName": "ECommerceApp.API", "projectPath": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.API\\ECommerceApp.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\ECommerceApp.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\ECommerceApp.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\ECommerceApp.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\ECommerceApp.Shared.csproj", "projectName": "ECommerceApp.Shared", "projectPath": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\ECommerceApp.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\DSI23 semester 2\\projet .net\\projet4\\ECommerceApp.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}