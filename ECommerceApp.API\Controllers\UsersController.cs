using System.Security.Claims;
using System.Threading.Tasks;
using ECommerceApp.API.Data;
using ECommerceApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace ECommerceApp.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public UsersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Users/<USER>
        [HttpGet("profile")]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetUserProfile()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            var user = await _context.Users.FindAsync(userId);

            if (user == null)
            {
                return NotFound(ApiResponse<UserProfileDto>.ErrorResponse("Utilisateur non trouvé"));
            }

            var userProfile = new UserProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Address = user.Address,
                PhoneNumber = user.PhoneNumber,
                Role = user.Role
            };

            return Ok(ApiResponse<UserProfileDto>.SuccessResponse(userProfile));
        }

        // GET: api/Users
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse<List<UserDto>>>> GetUsers()
        {
            var users = await _context.Users.ToListAsync();
            var userDtos = users.Select(user => new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Address = user.Address,
                PhoneNumber = user.PhoneNumber,
                Role = user.Role
            }).ToList();

            return Ok(ApiResponse<List<UserDto>>.SuccessResponse(userDtos));
        }

        // PUT: api/Users/<USER>
        [HttpPut("profile")]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> UpdateUserProfile(UserUpdateDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<UserProfileDto>.ErrorResponse("Données invalides", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }

            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            var user = await _context.Users.FindAsync(userId);

            if (user == null)
            {
                return NotFound(ApiResponse<UserProfileDto>.ErrorResponse("Utilisateur non trouvé"));
            }

            if (model.Email != user.Email && await _context.Users.AnyAsync(u => u.Email == model.Email && u.Id != userId))
            {
                return BadRequest(ApiResponse<UserProfileDto>.ErrorResponse("Cet email est déjà utilisé par un autre utilisateur"));
            }

            user.Username = model.Username;
            user.Email = model.Email;
            user.Address = model.Address;
            user.PhoneNumber = model.PhoneNumber;

            if (!string.IsNullOrEmpty(model.Password))
            {
                if (model.Password != model.ConfirmPassword)
                {
                    return BadRequest(ApiResponse<UserProfileDto>.ErrorResponse("Les mots de passe ne correspondent pas"));
                }

                user.Password = model.Password; 
            }

            _context.Entry(user).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            var userProfile = new UserProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Address = user.Address,
                PhoneNumber = user.PhoneNumber,
                Role = user.Role
            };

            return Ok(ApiResponse<UserProfileDto>.SuccessResponse(userProfile, "Profil mis à jour avec succès"));
        }
    }
}
