using System;

namespace ECommerceApp.Shared.Models
{
    public class OrderStatisticsDto
    {
        public int TotalOrders { get; set; }
        public decimal TotalSpent { get; set; }
        public int CompletedOrders { get; set; }
        public int PendingOrders { get; set; }
        public DateTime? LastOrderDate { get; set; }
        public string MostOrderedProduct { get; set; } = "Aucun produit commandé";

        // Solde fictif pour l'utilisateur (points de fidélité)
        public decimal Balance { get; set; } = 100.00m; // Valeur par défaut
    }
}
