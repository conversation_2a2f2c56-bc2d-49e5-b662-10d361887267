@page "/products/{Id:int}"
@inject ProductService ProductService
@inject CartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Détail du produit - E-Commerce App</PageTitle>

@if (product == null)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-md-6">
            @if (!string.IsNullOrEmpty(product.ImageUrl))
            {
                <img src="@product.ImageUrl" class="img-fluid rounded" alt="@product.Name"
                     onerror="this.onerror=null; this.src='https://picsum.photos/600/400?random=@product.Id';">
            }
            else
            {
                <img src="https://picsum.photos/600/400?random=@product.Id" class="img-fluid rounded" alt="@product.Name">
            }
        </div>
        <div class="col-md-6">
            <h1>@product.Name</h1>
            <p class="lead">@product.Description</p>
            <h3 class="text-primary">@product.Price.ToString("C")</h3>

            <p>
                @if (product.StockQuantity > 0)
                {
                    <span class="badge bg-success">En stock (@product.StockQuantity disponibles)</span>
                }
                else
                {
                    <span class="badge bg-danger">Rupture de stock</span>
                }
            </p>

            <div class="d-flex align-items-center mt-4">
                <div class="input-group me-3" style="width: 150px;">
                    <button class="btn btn-outline-secondary" type="button" @onclick="DecreaseQuantity" disabled="@(quantity <= 1)">-</button>
                    <input type="number" class="form-control text-center" @bind="quantity" min="1" max="@product.StockQuantity">
                    <button class="btn btn-outline-secondary" type="button" @onclick="IncreaseQuantity" disabled="@(quantity >= product.StockQuantity)">+</button>
                </div>

                <button class="btn btn-success" @onclick="AddToCart" disabled="@(product.StockQuantity <= 0)">
                    <i class="oi oi-cart"></i> Ajouter au panier
                </button>
            </div>

            <div class="mt-4">
                <a href="/products" class="btn btn-outline-primary">
                    <i class="oi oi-arrow-left"></i> Retour aux produits
                </a>
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ProductDto product;
    private int quantity = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadProduct();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadProduct();
    }

    private async Task LoadProduct()
    {
        var response = await ProductService.GetProduct(Id);
        if (response.Success)
        {
            product = response.Data;
            quantity = 1; // Réinitialiser la quantité
        }
        else
        {
            NavigationManager.NavigateTo("/products");
            await JSRuntime.InvokeVoidAsync("alert", "Produit non trouvé.");
        }
    }

    private void IncreaseQuantity()
    {
        if (quantity < product.StockQuantity)
        {
            quantity++;
        }
    }

    private void DecreaseQuantity()
    {
        if (quantity > 1)
        {
            quantity--;
        }
    }

    private async Task AddToCart()
    {
        if (product.StockQuantity <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Ce produit est en rupture de stock.");
            return;
        }

        if (quantity > product.StockQuantity)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Il n'y a que {product.StockQuantity} unités disponibles.");
            quantity = product.StockQuantity;
            return;
        }

        for (int i = 0; i < quantity; i++)
        {
            await CartService.AddToCart(product);
        }

        await JSRuntime.InvokeVoidAsync("alert", $"{quantity} {product.Name} {(quantity > 1 ? "ont été ajoutés" : "a été ajouté")} à votre panier.");
    }
}
