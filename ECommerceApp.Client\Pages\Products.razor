@page "/products"
@inject ProductService ProductService
@inject CartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Produits - E-Commerce App</PageTitle>

<h1>Nos Produits</h1>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Rechercher un produit..." @bind="searchTerm" @bind:event="oninput">
            <button class="btn btn-outline-secondary" type="button" @onclick="FilterProducts">
                <i class="oi oi-magnifying-glass"></i> Rechercher
            </button>
        </div>
    </div>
</div>

@if (products == null)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else if (filteredProducts.Count == 0)
{
    <div class="alert alert-info">
        Aucun produit trouvé.
    </div>
}
else
{
    <div class="row">
        @foreach (var product in filteredProducts)
        {
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    @if (!string.IsNullOrEmpty(product.ImageUrl))
                    {
                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name"
                             style="height: 200px; object-fit: cover;"
                             onerror="this.onerror=null; this.src='https://picsum.photos/300/200?random=@product.Id';">
                    }
                    else
                    {
                        <img src="https://picsum.photos/300/200?random=@product.Id" class="card-img-top" alt="@product.Name"
                             style="height: 200px; object-fit: cover;">
                    }
                    <div class="card-body">
                        <h5 class="card-title">@product.Name</h5>
                        <p class="card-text">@(product.Description.Length > 100 ? product.Description.Substring(0, 100) + "..." : product.Description)</p>
                        <p class="card-text"><strong>Prix: @product.Price.ToString("C")</strong></p>
                        <p class="card-text">
                            <small class="text-muted">
                                @if (product.StockQuantity > 0)
                                {
                                    <span class="text-success">En stock (@product.StockQuantity disponibles)</span>
                                }
                                else
                                {
                                    <span class="text-danger">Rupture de stock</span>
                                }
                            </small>
                        </p>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <button class="btn btn-outline-primary" @onclick="() => NavigateToProductDetail(product.Id)">
                            <i class="oi oi-eye"></i> Détails
                        </button>
                        <button class="btn btn-success" @onclick="() => AddToCart(product)" disabled="@(product.StockQuantity <= 0)">
                            <i class="oi oi-cart"></i> Ajouter au panier
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
}

@code {
    private List<ProductDto> products;
    private List<ProductDto> filteredProducts = new List<ProductDto>();
    private string searchTerm = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        var response = await ProductService.GetProducts();
        if (response.Success)
        {
            products = response.Data;
            FilterProducts();
        }
        else
        {
            products = new List<ProductDto>();
            filteredProducts = new List<ProductDto>();
        }
    }

    private void FilterProducts()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredProducts = products.ToList();
        }
        else
        {
            filteredProducts = products
                .Where(p => p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                           p.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }
    }

    private void NavigateToProductDetail(int id)
    {
        NavigationManager.NavigateTo($"/products/{id}");
    }

    private async Task AddToCart(ProductDto product)
    {
        if (product.StockQuantity <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Ce produit est en rupture de stock.");
            return;
        }

        await CartService.AddToCart(product);
        await JSRuntime.InvokeVoidAsync("alert", $"{product.Name} a été ajouté à votre panier.");
    }
}
