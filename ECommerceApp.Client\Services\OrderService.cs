using System.Collections.Generic;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;

namespace ECommerceApp.Client.Services
{
    public class OrderService
    {
        private readonly HttpService _httpService;

        public OrderService(HttpService httpService)
        {
            _httpService = httpService;
        }

        public async Task<ApiResponse<List<OrderDto>>> GetOrders()
        {
            return await _httpService.Get<List<OrderDto>>("api/orders");
        }

        public async Task<ApiResponse<OrderDto>> GetOrder(int id)
        {
            return await _httpService.Get<OrderDto>($"api/orders/{id}");
        }

        public async Task<ApiResponse<OrderDto>> CreateOrder(CreateOrderDto model)
        {
            return await _httpService.Post<OrderDto>("api/orders", model);
        }

        public async Task<ApiResponse<OrderDto>> UpdateOrderStatus(int id, UpdateOrderStatusDto model)
        {
            return await _httpService.Put<OrderDto>($"api/orders/{id}/status", model);
        }

        public async Task<OrderStatisticsDto> GetOrderStatistics()
        {
            try
            {
                var orders = await GetOrders();
                if (!orders.Success || orders.Data == null || orders.Data.Count == 0)
                {
                    return new OrderStatisticsDto();
                }

                var statistics = new OrderStatisticsDto
                {
                    TotalOrders = orders.Data.Count,
                    TotalSpent = orders.Data.Sum(o => o.TotalAmount),
                    CompletedOrders = orders.Data.Count(o => o.Status == "Livré"),
                    PendingOrders = orders.Data.Count(o => o.Status == "En cours" || o.Status == "En attente"),
                    LastOrderDate = orders.Data.OrderByDescending(o => o.OrderDate).FirstOrDefault()?.OrderDate,
                    MostOrderedProduct = GetMostOrderedProduct(orders.Data),
                    // Calculer un solde fictif basé sur les achats (10% du total dépensé)
                    Balance = Math.Round(orders.Data.Sum(o => o.TotalAmount) * 0.1m + 100.00m, 2)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting order statistics: {ex.Message}");
                return new OrderStatisticsDto();
            }
        }

        private string GetMostOrderedProduct(List<OrderDto> orders)
        {
            try
            {
                var allItems = orders.SelectMany(o => o.OrderItems).ToList();

                if (allItems.Count == 0)
                {
                    return "Aucun produit commandé";
                }

                var groupedItems = allItems
                    .GroupBy(item => item.ProductId)
                    .Select(group => new
                    {
                        ProductId = group.Key,
                        ProductName = group.First().ProductName,
                        TotalQuantity = group.Sum(item => item.Quantity)
                    })
                    .OrderByDescending(x => x.TotalQuantity)
                    .FirstOrDefault();

                return groupedItems?.ProductName ?? "Aucun produit commandé";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting most ordered product: {ex.Message}");
                return "Erreur lors du calcul";
            }
        }
    }
}
