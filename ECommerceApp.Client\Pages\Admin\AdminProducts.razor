@page "/admin/products"
@attribute [Authorize(Roles = "Admin")]
@inject ProductService ProductService
@inject IJSRuntime JSRuntime

<PageTitle>Gestion des Produits - E-Commerce App</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Gestion des Produits</h1>
    <button class="btn btn-primary" @onclick="ShowAddForm">Ajouter un produit</button>
</div>

@if (products == null)
{
    <p>Chargement...</p>
}
else if (products.Count == 0)
{
    <div class="alert alert-info">
        <p>Aucun produit disponible. Cliquez sur "Ajouter un produit" pour commencer.</p>
    </div>
}
else
{
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nom</th>
                    <th>Prix</th>
                    <th>Stock</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var product in products)
                {
                    <tr>
                        <td>@product.Id</td>
                        <td>@product.Name</td>
                        <td>@product.Price.ToString("C")</td>
                        <td>@product.StockQuantity</td>
                        <td>
                            <button class="btn btn-sm btn-info me-1" @onclick="@(e => ShowEditForm(product))">Modifier</button>
                            <button class="btn btn-sm btn-danger" @onclick="@(e => DeleteProduct(product.Id))">Supprimer</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}

@if (showForm)
{
    <div class="card mt-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">@formTitle</h3>
        </div>
        <div class="card-body">
            @if (isCreating)
            {
                <EditForm Model="createModel" OnValidSubmit="HandleCreateSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="name" class="form-label">Nom</label>
                        <InputText id="name" class="form-control" @bind-Value="createModel.Name" />
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <InputTextArea id="description" class="form-control" @bind-Value="createModel.Description" rows="3" />
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">Prix</label>
                        <InputNumber id="price" class="form-control" @bind-Value="createModel.Price" />
                    </div>

                    <div class="mb-3">
                        <label for="stockQuantity" class="form-label">Quantité en stock</label>
                        <InputNumber id="stockQuantity" class="form-control" @bind-Value="createModel.StockQuantity" />
                    </div>

                    <div class="mb-3">
                        <label for="imageUrl" class="form-label">URL de l'image</label>
                        <InputText id="imageUrl" class="form-control" @bind-Value="createModel.ImageUrl" />
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" @onclick="HideForm">Annuler</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div>
                </EditForm>
            }
            else
            {
                <EditForm Model="updateModel" OnValidSubmit="HandleUpdateSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="name" class="form-label">Nom</label>
                        <InputText id="name" class="form-control" @bind-Value="updateModel.Name" />
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <InputTextArea id="description" class="form-control" @bind-Value="updateModel.Description" rows="3" />
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">Prix</label>
                        <InputNumber id="price" class="form-control" @bind-Value="updateModel.Price" />
                    </div>

                    <div class="mb-3">
                        <label for="stockQuantity" class="form-label">Quantité en stock</label>
                        <InputNumber id="stockQuantity" class="form-control" @bind-Value="updateModel.StockQuantity" />
                    </div>

                    <div class="mb-3">
                        <label for="imageUrl" class="form-label">URL de l'image</label>
                        <InputText id="imageUrl" class="form-control" @bind-Value="updateModel.ImageUrl" />
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" @onclick="HideForm">Annuler</button>
                        <button type="submit" class="btn btn-primary">Mettre à jour</button>
                    </div>
                </EditForm>
            }
        </div>
    </div>
}

@code {
    private List<ProductDto> products;
    private bool showForm = false;
    private bool isCreating = true;
    private string formTitle = "";
    private int currentProductId;

    private ProductCreateDto createModel = new ProductCreateDto();
    private ProductUpdateDto updateModel = new ProductUpdateDto();

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        var response = await ProductService.GetProducts();
        if (response.Success)
        {
            products = response.Data;
        }
        else
        {
            products = new List<ProductDto>();
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
        }
    }

    private void ShowAddForm()
    {
        isCreating = true;
        formTitle = "Ajouter un produit";
        createModel = new ProductCreateDto();
        showForm = true;
    }

    private void ShowEditForm(ProductDto product)
    {
        isCreating = false;
        formTitle = "Modifier le produit";
        currentProductId = product.Id;

        updateModel = new ProductUpdateDto
        {
            Name = product.Name,
            Description = product.Description,
            Price = product.Price,
            StockQuantity = product.StockQuantity,
            ImageUrl = product.ImageUrl
        };

        showForm = true;
    }

    private void HideForm()
    {
        showForm = false;
    }

    private async Task HandleCreateSubmit()
    {
        var response = await ProductService.CreateProduct(createModel);
        if (response.Success)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Produit créé avec succès!");
            showForm = false;
            await LoadProducts();
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
        }
    }

    private async Task HandleUpdateSubmit()
    {
        var response = await ProductService.UpdateProduct(currentProductId, updateModel);
        if (response.Success)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Produit mis à jour avec succès!");
            showForm = false;
            await LoadProducts();
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
        }
    }

    private async Task DeleteProduct(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer ce produit?"))
        {
            var response = await ProductService.DeleteProduct(id);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Produit supprimé avec succès!");
                await LoadProducts();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
    }
}
