﻿<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized Context="authContext">
                    @if (!authContext.User.Identity.IsAuthenticated)
                    {
                        <RedirectLogin />
                    }
                    else
                    {
                        <PageTitle>Accès refusé</PageTitle>
                        <LayoutView Layout="@typeof(MainLayout)">
                            <div class="alert alert-danger">
                                <h3>Accès refusé</h3>
                                <p>Vous n'avez pas les autorisations nécessaires pour accéder à cette page.</p>
                            </div>
                        </LayoutView>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Page non trouvée</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="alert alert-warning">
                    <h3>Page non trouvée</h3>
                    <p>La page que vous recherchez n'existe pas.</p>
                    <a href="/" class="btn btn-primary">Retour à l'accueil</a>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

@code {
    public class RedirectLogin : ComponentBase
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        protected override void OnInitialized()
        {
            NavigationManager.NavigateTo($"/login?returnUrl={Uri.EscapeDataString(NavigationManager.Uri)}", true);
        }
    }
}
