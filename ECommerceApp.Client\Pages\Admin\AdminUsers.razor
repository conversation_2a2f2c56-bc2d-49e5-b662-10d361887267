@page "/admin/users"
@attribute [Authorize(Roles = "Admin")]
@inject AuthService AuthService
@inject IJSRuntime JSRuntime

<PageTitle>Gestion des Clients - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/admin">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Gestion des Clients</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-0"><i class="oi oi-people me-2"></i>Gestion des Clients</h1>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center py-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Recherche -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Rechercher un client par nom ou email" @bind="searchTerm" @bind:event="oninput" />
                    <button class="btn btn-outline-secondary" type="button" @onclick="ApplySearch">
                        <i class="oi oi-magnifying-glass"></i>
                    </button>
                </div>
            </div>
        </div>

        @if (filteredUsers.Count == 0)
        {
            <div class="alert alert-info">
                <p class="mb-0">Aucun client trouvé.</p>
            </div>
        }
        else
        {
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Nom d'utilisateur</th>
                                    <th>Email</th>
                                    <th>Rôle</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in filteredUsers)
                                {
                                    <tr>
                                        <td>@user.Id</td>
                                        <td>@user.Username</td>
                                        <td>@user.Email</td>
                                        <td>
                                            @if (user.Role == "Admin")
                                            {
                                                <span class="badge bg-danger">@user.Role</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">@user.Role</span>
                                            }
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" @onclick="() => ShowUserDetails(user)">
                                                <i class="oi oi-eye me-1"></i> Détails
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }
</div>

<!-- Modal de détails utilisateur -->
@if (selectedUser != null)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails du client #@selectedUser.Id</h5>
                    <button type="button" class="btn-close" @onclick="CloseModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">Informations personnelles</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6 class="text-muted mb-1">Nom d'utilisateur</h6>
                                        <p class="mb-0 fs-5">@selectedUser.Username</p>
                                    </div>
                                    <div class="mb-3">
                                        <h6 class="text-muted mb-1">Email</h6>
                                        <p class="mb-0 fs-5">@selectedUser.Email</p>
                                    </div>
                                    <div class="mb-3">
                                        <h6 class="text-muted mb-1">Rôle</h6>
                                        <p class="mb-0">
                                            @if (selectedUser.Role == "Admin")
                                            {
                                                <span class="badge bg-danger">@selectedUser.Role</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">@selectedUser.Role</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">Coordonnées</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6 class="text-muted mb-1">Adresse</h6>
                                        <p class="mb-0 fs-5">@(string.IsNullOrEmpty(selectedUser.Address) ? "Non spécifiée" : selectedUser.Address)</p>
                                    </div>
                                    <div class="mb-3">
                                        <h6 class="text-muted mb-1">Téléphone</h6>
                                        <p class="mb-0 fs-5">@(string.IsNullOrEmpty(selectedUser.PhoneNumber) ? "Non spécifié" : selectedUser.PhoneNumber)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (userOrders != null)
                    {
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Commandes (@userOrders.Count)</h5>
                            </div>
                            <div class="card-body p-0">
                                @if (userOrders.Count == 0)
                                {
                                    <div class="p-3">
                                        <p class="mb-0">Aucune commande trouvée pour ce client.</p>
                                    </div>
                                }
                                else
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped mb-0">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Date</th>
                                                    <th>Montant</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var order in userOrders)
                                                {
                                                    <tr>
                                                        <td>@order.Id</td>
                                                        <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                                        <td>@order.TotalAmount.ToString("C")</td>
                                                        <td>
                                                            @switch (order.Status)
                                                            {
                                                                case "En attente":
                                                                    <span class="badge bg-warning">@order.Status</span>
                                                                    break;
                                                                case "En cours":
                                                                    <span class="badge bg-info">@order.Status</span>
                                                                    break;
                                                                case "Livré":
                                                                    <span class="badge bg-success">@order.Status</span>
                                                                    break;
                                                                default:
                                                                    <span class="badge bg-secondary">@order.Status</span>
                                                                    break;
                                                            }
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    @if (userComplaints != null)
                    {
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Réclamations (@userComplaints.Count)</h5>
                            </div>
                            <div class="card-body p-0">
                                @if (userComplaints.Count == 0)
                                {
                                    <div class="p-3">
                                        <p class="mb-0">Aucune réclamation trouvée pour ce client.</p>
                                    </div>
                                }
                                else
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped mb-0">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Sujet</th>
                                                    <th>Date</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var complaint in userComplaints)
                                                {
                                                    <tr>
                                                        <td>@complaint.Id</td>
                                                        <td>@complaint.Subject</td>
                                                        <td>@complaint.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                                        <td>
                                                            @switch (complaint.Status)
                                                            {
                                                                case "Ouvert":
                                                                    <span class="badge bg-danger">@complaint.Status</span>
                                                                    break;
                                                                case "En cours":
                                                                    <span class="badge bg-warning">@complaint.Status</span>
                                                                    break;
                                                                case "Résolu":
                                                                    <span class="badge bg-success">@complaint.Status</span>
                                                                    break;
                                                                default:
                                                                    <span class="badge bg-secondary">@complaint.Status</span>
                                                                    break;
                                                            }
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary" @onclick="CloseModal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private List<UserDto> users;
    private List<UserDto> filteredUsers = new List<UserDto>();
    private bool isLoading = true;
    private string searchTerm = "";
    private UserDto selectedUser;
    private List<OrderDto> userOrders;
    private List<ComplaintDto> userComplaints;

    [Inject]
    private OrderService OrderService { get; set; }

    [Inject]
    private ComplaintService ComplaintService { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            var response = await AuthService.GetUsers();
            if (response.Success)
            {
                users = response.Data;
                filteredUsers = users.ToList();
            }
            else
            {
                users = new List<UserDto>();
                filteredUsers = new List<UserDto>();
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ApplySearch()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredUsers = users.ToList();
        }
        else
        {
            filteredUsers = users
                .Where(u =>
                    u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    u.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }
    }

    private async Task ShowUserDetails(UserDto user)
    {
        selectedUser = user;

        // Charger les commandes de l'utilisateur
        try
        {
            var ordersResponse = await OrderService.GetOrders();
            if (ordersResponse.Success)
            {
                userOrders = ordersResponse.Data
                    .Where(o => o.UserId == user.Id)
                    .ToList();
            }
            else
            {
                userOrders = new List<OrderDto>();
            }
        }
        catch (Exception)
        {
            userOrders = new List<OrderDto>();
        }

        // Charger les réclamations de l'utilisateur
        try
        {
            var complaintsResponse = await ComplaintService.GetComplaints();
            if (complaintsResponse.Success)
            {
                userComplaints = complaintsResponse.Data
                    .Where(c => c.UserId == user.Id)
                    .ToList();
            }
            else
            {
                userComplaints = new List<ComplaintDto>();
            }
        }
        catch (Exception)
        {
            userComplaints = new List<ComplaintDto>();
        }
    }

    private void CloseModal()
    {
        selectedUser = null;
        userOrders = null;
        userComplaints = null;
    }
}