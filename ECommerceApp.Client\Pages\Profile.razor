@page "/profile"
@attribute [Authorize]
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Mon Profil - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Mon Profil</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-0"><i class="oi oi-person me-2"></i>Mon Profil</h1>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center py-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Profil</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <div class="avatar-circle mx-auto bg-primary text-white d-flex align-items-center justify-content-center" style="width: 120px; height: 120px; font-size: 3rem; border-radius: 50%;">
                                <span>@(string.IsNullOrEmpty(model.Username) ? "U" : model.Username.Substring(0, 1).ToUpper())</span>
                            </div>
                            <h3 class="mt-3 mb-1">@model.Username</h3>
                            <p class="text-muted">@model.Email</p>
                            <div class="badge bg-success mb-3">Client</div>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Adresse</h6>
                            <p class="mb-0">@(string.IsNullOrEmpty(model.Address) ? "Non spécifiée" : model.Address)</p>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Téléphone</h6>
                            <p class="mb-0">@(string.IsNullOrEmpty(model.PhoneNumber) ? "Non spécifié" : model.PhoneNumber)</p>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Membre depuis</h6>
                            <p class="mb-0">@DateTime.Now.ToString("MMMM yyyy")</p>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <a href="/orders" class="btn btn-outline-primary">
                                <i class="oi oi-list me-2"></i> Mes commandes
                            </a>
                            <a href="/complaints" class="btn btn-outline-primary">
                                <i class="oi oi-comment-square me-2"></i> Mes réclamations
                            </a>
                            <a href="/cart" class="btn btn-outline-primary">
                                <i class="oi oi-basket me-2"></i> Mon Panier
                            </a>
                            <button type="button" class="btn btn-danger" @onclick="Logout">
                                <i class="oi oi-account-logout me-2"></i> Déconnexion
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private UserUpdateDto model = new UserUpdateDto();
    private bool isLoading = true;
    private bool isProcessing = false;
    private string errorMessage;
    private string successMessage;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserProfile();
    }

    private async Task LoadUserProfile()
    {
        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            // Initialiser le modèle avec des valeurs par défaut
            model = new UserUpdateDto
            {
                Username = "",
                Email = "",
                Address = "",
                PhoneNumber = "",
                Password = "",
                ConfirmPassword = ""
            };

            var response = await AuthService.GetUserProfile();
            if (response.Success && response.Data != null)
            {
                model.Username = response.Data.Username ?? "";
                model.Email = response.Data.Email ?? "";
                model.Address = response.Data.Address ?? "";
                model.PhoneNumber = response.Data.PhoneNumber ?? "";
                model.Password = "";
                model.ConfirmPassword = "";

                Console.WriteLine($"Profil chargé: {model.Username}, {model.Email}, {model.Address}, {model.PhoneNumber}");
            }
            else
            {
                errorMessage = response.Message;
                Console.WriteLine($"Erreur lors du chargement du profil: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
            Console.WriteLine($"Exception lors du chargement du profil: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task UpdateProfile()
    {
        isProcessing = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            if (string.IsNullOrWhiteSpace(model.Username))
            {
                errorMessage = "Le nom d'utilisateur est obligatoire";
                isProcessing = false;
                return;
            }

            if (string.IsNullOrWhiteSpace(model.Email))
            {
                errorMessage = "L'email est obligatoire";
                isProcessing = false;
                return;
            }

            Console.WriteLine($"Mise à jour du profil: {model.Username}, {model.Email}, {model.Address}, {model.PhoneNumber}");

            var response = await AuthService.UpdateUserProfile(model);
            if (response.Success)
            {
                successMessage = "Profil mis à jour avec succès !";
                model.Password = "";
                model.ConfirmPassword = "";

                Console.WriteLine("Profil mis à jour avec succès");

                await LoadUserProfile();
            }
            else
            {
                errorMessage = response.Message;
                if (response.Errors != null && response.Errors.Any())
                {
                    errorMessage = string.Join(", ", response.Errors);
                }
                Console.WriteLine($"Erreur lors de la mise à jour du profil: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
            Console.WriteLine($"Exception lors de la mise à jour du profil: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }

    private async Task Logout()
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir vous déconnecter ?"))
        {
            await AuthService.Logout();
            NavigationManager.NavigateTo("/");
        }
    }
}
