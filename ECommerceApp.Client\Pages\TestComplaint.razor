@page "/test-complaint"
@inject ComplaintService ComplaintService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<PageTitle>Test Réclamation - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-2">Test de Réclamation</h1>
            <p class="lead">Cette page permet de tester l'envoi d'une réclamation.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">Formulaire de test</h5>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger mb-4">
                            <i class="oi oi-warning me-2"></i> @errorMessage
                        </div>
                    }

                    <EditForm Model="@complaintModel" OnValidSubmit="SubmitComplaint">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label for="subject" class="form-label">Sujet</label>
                            <InputText id="subject" class="form-control" @bind-Value="complaintModel.Subject" />
                            <ValidationMessage For="@(() => complaintModel.Subject)" />
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <InputTextArea id="description" class="form-control" @bind-Value="complaintModel.Description" rows="5" />
                            <ValidationMessage For="@(() => complaintModel.Description)" />
                            <div class="form-text">Décrivez votre problème en détail (minimum 10 caractères).</div>
                        </div>

                        <div class="mb-3">
                            <label for="productId" class="form-label">ID du Produit</label>
                            <InputNumber id="productId" class="form-control" @bind-Value="complaintModel.ProductId" />
                            <ValidationMessage For="@(() => complaintModel.ProductId)" />
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                @if (isSubmitting)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span>Envoi en cours...</span>
                                }
                                else
                                {
                                    <i class="oi oi-check me-2"></i>
                                    <span>Envoyer la réclamation</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>

            <div class="mt-4">
                <button class="btn btn-outline-secondary" @onclick="TestSerialization">
                    <i class="oi oi-code me-2"></i> Tester la sérialisation
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    private CreateComplaintDto complaintModel = new CreateComplaintDto
    {
        Subject = "Test de réclamation",
        Description = "Ceci est un test de réclamation pour vérifier le fonctionnement du système.",
        ProductId = 1
    };
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;

    private async Task SubmitComplaint()
    {
        isSubmitting = true;
        errorMessage = string.Empty;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(complaintModel);
            Console.WriteLine($"Données à envoyer: {json}");

            var response = await ComplaintService.CreateComplaint(complaintModel);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Votre réclamation a été envoyée avec succès. Nous la traiterons dans les plus brefs délais.");
                NavigationManager.NavigateTo("/complaints");
            }
            else
            {
                errorMessage = response.Message;
                Console.WriteLine($"Erreur lors de l'envoi de la réclamation: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
            Console.WriteLine($"Exception lors de l'envoi de la réclamation: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task TestSerialization()
    {
        try
        {
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };

            var json = System.Text.Json.JsonSerializer.Serialize(complaintModel, options);
            Console.WriteLine($"Sérialisation réussie: {json}");
            await JSRuntime.InvokeVoidAsync("alert", $"Sérialisation réussie: {json}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur de sérialisation: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur de sérialisation: {ex.Message}");
        }
    }
}
