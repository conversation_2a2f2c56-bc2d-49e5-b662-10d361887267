using System.Collections.Generic;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;

namespace ECommerceApp.Client.Services
{
    public class ProductService
    {
        private readonly HttpService _httpService;

        public ProductService(HttpService httpService)
        {
            _httpService = httpService;
        }

        public async Task<ApiResponse<List<ProductDto>>> GetProducts()
        {
            return await _httpService.Get<List<ProductDto>>("api/products");
        }

        public async Task<ApiResponse<ProductDto>> GetProduct(int id)
        {
            return await _httpService.Get<ProductDto>($"api/products/{id}");
        }

        public async Task<ApiResponse<ProductDto>> CreateProduct(ProductCreateDto model)
        {
            return await _httpService.Post<ProductDto>("api/products", model);
        }

        public async Task<ApiResponse<ProductDto>> UpdateProduct(int id, ProductUpdateDto model)
        {
            return await _httpService.Put<ProductDto>($"api/products/{id}", model);
        }

        public async Task<ApiResponse<bool>> DeleteProduct(int id)
        {
            return await _httpService.Delete<bool>($"api/products/{id}");
        }
    }
}
