@page "/complaints/create/{ProductId:int}"
@attribute [Authorize]
@inject ComplaintService ComplaintService
@inject ProductService ProductService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Nouvelle Réclamation - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item"><a href="/products">Produits</a></li>
                    <li class="breadcrumb-item"><a href="/complaints">Mes Réclamations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Nouvelle Réclamation</li>
                </ol>
            </nav>
            <h1 class="display-5 fw-bold mb-4"><i class="oi oi-warning me-2"></i>Nouvelle Réclamation</h1>
        </div>
    </div>

    @if (product == null)
    {
        <div class="d-flex justify-content-center my-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Produit concerné</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" alt="@product.Name" class="img-fluid rounded mb-3"
                                     style="max-height: 200px; object-fit: contain;"
                                     onerror="this.onerror=null; this.src='https://picsum.photos/300/200?random=@product.Id';">
                            }
                            else
                            {
                                <img src="https://picsum.photos/300/200?random=@product.Id" class="img-fluid rounded mb-3"
                                     alt="@product.Name" style="max-height: 200px; object-fit: contain;">
                            }
                        </div>
                        <h5 class="card-title mb-2">@product.Name</h5>
                        <p class="text-muted mb-2">Prix: @product.Price.ToString("C")</p>
                        <p class="card-text small">@product.Description</p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Détails de la réclamation</h5>
                    </div>
                    <div class="card-body p-4">
                        <EditForm Model="@complaintModel" OnValidSubmit="SubmitComplaint">
                            <DataAnnotationsValidator />
                            <ValidationSummary class="text-danger mb-4" />

                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="oi oi-warning me-2"></i> @errorMessage
                                    <button type="button" class="btn-close" @onclick="() => errorMessage = string.Empty"></button>
                                </div>
                            }

                            <div class="mb-3">
                                <label for="subject" class="form-label">Sujet</label>
                                <InputText id="subject" class="form-control" @bind-Value="complaintModel.Subject" placeholder="Ex: Produit défectueux, Livraison en retard, etc." />
                                <ValidationMessage For="@(() => complaintModel.Subject)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description détaillée</label>
                                <InputTextArea id="description" class="form-control" @bind-Value="complaintModel.Description" rows="6" placeholder="Décrivez en détail le problème rencontré avec ce produit..." />
                                <ValidationMessage For="@(() => complaintModel.Description)" class="text-danger" />
                                <div class="form-text">Soyez précis et incluez toutes les informations pertinentes pour nous aider à traiter votre réclamation.</div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <a href="/complaints" class="btn btn-outline-secondary">
                                    <i class="oi oi-arrow-left me-2"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                    @if (isSubmitting)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                        <span>Envoi en cours...</span>
                                    }
                                    else
                                    {
                                        <i class="oi oi-check me-2"></i>
                                        <span>Envoyer la réclamation</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    public int ProductId { get; set; }

    private ProductDto product;
    private CreateComplaintDto complaintModel = new CreateComplaintDto
    {
        Subject = string.Empty,
        Description = string.Empty
    };
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadProduct();
        complaintModel.ProductId = ProductId;
    }

    private async Task LoadProduct()
    {
        var response = await ProductService.GetProduct(ProductId);
        if (response.Success)
        {
            product = response.Data;
        }
        else
        {
            errorMessage = response.Message;
        }
    }

    private async Task SubmitComplaint()
    {
        isSubmitting = true;
        errorMessage = string.Empty;

        try
        {
            // Vérifier que les champs sont remplis
            if (string.IsNullOrWhiteSpace(complaintModel.Subject))
            {
                errorMessage = "Le sujet est requis.";
                isSubmitting = false;
                return;
            }

            if (string.IsNullOrWhiteSpace(complaintModel.Description) || complaintModel.Description.Length < 10)
            {
                errorMessage = "La description doit contenir au moins 10 caractères.";
                isSubmitting = false;
                return;
            }

            // S'assurer que le ProductId est correctement défini
            complaintModel.ProductId = ProductId;

            Console.WriteLine($"Envoi de la réclamation: Sujet={complaintModel.Subject}, Description={complaintModel.Description}, ProductId={complaintModel.ProductId}");

            var response = await ComplaintService.CreateComplaint(complaintModel);

            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Votre réclamation a été envoyée avec succès. Nous la traiterons dans les plus brefs délais.");
                NavigationManager.NavigateTo("/complaints");
            }
            else
            {
                errorMessage = response.Message;
                Console.WriteLine($"Erreur lors de l'envoi de la réclamation: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
            Console.WriteLine($"Exception lors de l'envoi de la réclamation: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
