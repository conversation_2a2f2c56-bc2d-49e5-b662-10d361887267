@page "/register"
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Inscription - E-Commerce App</PageTitle>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Inscription</h3>
            </div>
            <div class="card-body">
                <EditForm Model="@model" OnValidSubmit="HandleRegistration">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            @errorMessage
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Nom d'utilisateur</label>
                            <InputText id="username" class="form-control" @bind-Value="model.Username" />
                            <ValidationMessage For="@(() => model.Username)" />
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <InputText id="email" class="form-control" @bind-Value="model.Email" />
                            <ValidationMessage For="@(() => model.Email)" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <InputText id="password" type="password" class="form-control" @bind-Value="model.Password" />
                            <ValidationMessage For="@(() => model.Password)" />
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                            <InputText id="confirmPassword" type="password" class="form-control" @bind-Value="model.ConfirmPassword" />
                            <ValidationMessage For="@(() => model.ConfirmPassword)" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Adresse</label>
                        <InputTextArea id="address" class="form-control" @bind-Value="model.Address" rows="2" />
                    </div>

                    <div class="mb-3">
                        <label for="phoneNumber" class="form-label">Numéro de téléphone</label>
                        <InputText id="phoneNumber" class="form-control" @bind-Value="model.PhoneNumber" />
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" disabled="@isLoading">
                            @if (isLoading)
                            {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span> Inscription en cours...</span>
                            }
                            else
                            {
                                <span>S'inscrire</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Vous avez déjà un compte ? <a href="login">Connectez-vous</a></p>
            </div>
        </div>
    </div>
</div>

@code {
    private UserRegistrationDto model = new UserRegistrationDto();
    private string errorMessage;
    private bool isLoading;

    protected override void OnInitialized()
    {
        if (AuthService.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/");
        }
    }

    private async Task HandleRegistration()
    {
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            var response = await AuthService.Register(model);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Inscription réussie ! Vous pouvez maintenant vous connecter.");
                NavigationManager.NavigateTo("/login");
            }
            else
            {
                errorMessage = response.Message;
                if (response.Errors != null && response.Errors.Any())
                {
                    errorMessage = string.Join(", ", response.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
