using System;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;
using Microsoft.JSInterop;

namespace ECommerceApp.Client.Services
{
    public class AuthService
    {
        private readonly HttpService _httpService;
        private readonly IJSRuntime _jsRuntime;
        private UserProfileDto _currentUser;

        public event Action OnAuthStateChanged;

        public AuthService(HttpService httpService, IJSRuntime jsRuntime)
        {
            _httpService = httpService;
            _jsRuntime = jsRuntime;
        }

        public UserProfileDto CurrentUser => _currentUser;

        public bool IsAuthenticated => _currentUser != null;

        public bool IsAdmin => _currentUser?.Role == "Admin";

        public async Task<bool> Initialize()
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            var userJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "user");
            if (string.IsNullOrEmpty(userJson))
            {
                return false;
            }

            try
            {
                _currentUser = System.Text.Json.JsonSerializer.Deserialize<UserProfileDto>(userJson);
                NotifyAuthStateChanged();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<ApiResponse<object>> Login(UserLoginDto model)
        {
            Console.WriteLine("Login attempt for: " + model.Email);
            var response = await _httpService.Post<object>("api/auth/login", model);
            Console.WriteLine("Login response success: " + response.Success);

            if (response.Success && response.Data != null)
            {
                Console.WriteLine("Login successful, processing user data");
                try
                {
                    var jsonData = System.Text.Json.JsonSerializer.Serialize(response.Data);
                    Console.WriteLine("Response data serialized: " + jsonData);

                    // Utiliser des options de désérialisation pour ignorer la casse
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var data = System.Text.Json.JsonSerializer.Deserialize<LoginResponse>(jsonData, options);

                    if (data == null)
                    {
                        Console.WriteLine("ERROR: Deserialized data is null");
                        return ApiResponse<object>.ErrorResponse("Erreur de désérialisation des données");
                    }

                    if (data.Token == null)
                    {
                        Console.WriteLine("ERROR: Token is null");
                        return ApiResponse<object>.ErrorResponse("Token manquant dans la réponse");
                    }

                    if (data.User == null)
                    {
                        Console.WriteLine("ERROR: User is null");
                        return ApiResponse<object>.ErrorResponse("Données utilisateur manquantes dans la réponse");
                    }

                    Console.WriteLine("Data deserialized successfully");
                    Console.WriteLine("Token: " + data.Token);
                    Console.WriteLine("User: " + System.Text.Json.JsonSerializer.Serialize(data.User));

                    await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "authToken", data.Token);
                    await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "user", System.Text.Json.JsonSerializer.Serialize(data.User));

                    _currentUser = data.User;
                    Console.WriteLine("Current user set: " + _currentUser.Username);
                    Console.WriteLine("Is admin: " + IsAdmin);
                    NotifyAuthStateChanged();
                    Console.WriteLine("Auth state change notified");
                }
                catch (Exception ex)
                {
                    Console.WriteLine("ERROR during login data processing: " + ex.Message);
                    Console.WriteLine("Stack trace: " + ex.StackTrace);
                    return ApiResponse<object>.ErrorResponse("Erreur lors du traitement des données de connexion: " + ex.Message);
                }
            }
            else
            {
                Console.WriteLine("Login failed: " + response.Message);
            }
            return response;
        }

        public async Task<ApiResponse<UserProfileDto>> Register(UserRegistrationDto model)
        {
            Console.WriteLine("Registering user: " + model.Username);
            try
            {
                var response = await _httpService.Post<UserProfileDto>("api/auth/register", model);
                Console.WriteLine("Registration response: " + (response.Success ? "Success" : "Failed"));
                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Registration exception: " + ex.Message);
                Console.WriteLine("Stack trace: " + ex.StackTrace);
                throw;
            }
        }

        public async Task Logout()
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "authToken");
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "user");
            _currentUser = null;
            NotifyAuthStateChanged();
        }

        public async Task<ApiResponse<UserProfileDto>> GetUserProfile()
        {
            var response = await _httpService.Get<UserProfileDto>("api/users/profile");
            if (response.Success && response.Data != null)
            {
                _currentUser = response.Data;
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "user", System.Text.Json.JsonSerializer.Serialize(_currentUser));
                NotifyAuthStateChanged();
            }
            return response;
        }

        public async Task<ApiResponse<UserProfileDto>> UpdateUserProfile(UserUpdateDto model)
        {
            var response = await _httpService.Put<UserProfileDto>("api/users/profile", model);
            if (response.Success && response.Data != null)
            {
                _currentUser = response.Data;
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "user", System.Text.Json.JsonSerializer.Serialize(_currentUser));
                NotifyAuthStateChanged();
            }
            return response;
        }

        public async Task<ApiResponse<List<UserDto>>> GetUsers()
        {
            if (!IsAdmin)
            {
                return ApiResponse<List<UserDto>>.ErrorResponse("Accès non autorisé");
            }

            return await _httpService.Get<List<UserDto>>("api/users");
        }

        private void NotifyAuthStateChanged()
        {
            Console.WriteLine("NotifyAuthStateChanged called");
            if (OnAuthStateChanged != null)
            {
                Console.WriteLine("Invoking OnAuthStateChanged event");
                OnAuthStateChanged.Invoke();
            }
            else
            {
                Console.WriteLine("WARNING: OnAuthStateChanged is null");
            }
        }

        private class LoginResponse
        {
            // Utiliser System.Text.Json.Serialization pour spécifier le nom exact des propriétés JSON
            [System.Text.Json.Serialization.JsonPropertyName("token")]
            public string? Token { get; set; }

            [System.Text.Json.Serialization.JsonPropertyName("user")]
            public UserProfileDto? User { get; set; }
        }
    }
}
