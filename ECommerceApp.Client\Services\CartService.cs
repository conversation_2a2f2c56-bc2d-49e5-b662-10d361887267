using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ECommerceApp.Shared.Models;
using Microsoft.JSInterop;

namespace ECommerceApp.Client.Services
{
    public class CartService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ProductService _productService;
        private readonly AuthService _authService;
        private List<CartItemDto> _cartItems = new List<CartItemDto>();

        public event Action OnCartChanged;

        public CartService(IJSRuntime jsRuntime, ProductService productService, AuthService authService)
        {
            _jsRuntime = jsRuntime;
            _productService = productService;
            _authService = authService;

            // S'abonner aux changements d'état d'authentification
            _authService.OnAuthStateChanged += async () => await Initialize();
        }

        public List<CartItemDto> CartItems => _cartItems;

        public int ItemCount => _cartItems.Sum(item => item.Quantity);

        public decimal TotalPrice => _cartItems.Sum(item => item.Subtotal);

        public async Task Initialize()
        {
            // Vider le panier d'abord
            _cartItems.Clear();

            // Si l'utilisateur n'est pas connecté, ne pas charger le panier
            if (!_authService.IsAuthenticated)
            {
                NotifyCartChanged();
                return;
            }

            // Utiliser l'ID de l'utilisateur comme partie de la clé du panier
            string cartKey = $"cart_{_authService.CurrentUser.Id}";
            Console.WriteLine($"Initializing cart for user ID: {_authService.CurrentUser.Id}");

            var cartJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", cartKey);
            if (!string.IsNullOrEmpty(cartJson))
            {
                try
                {
                    _cartItems = JsonSerializer.Deserialize<List<CartItemDto>>(cartJson) ?? new List<CartItemDto>();
                    Console.WriteLine($"Loaded cart with {_cartItems.Count} items for user {_authService.CurrentUser.Username}");
                    NotifyCartChanged();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading cart: {ex.Message}");
                    _cartItems = new List<CartItemDto>();
                }
            }
            else
            {
                Console.WriteLine("No cart found in localStorage");
            }
        }

        public async Task AddToCart(ProductDto product, int quantity = 1)
        {
            var existingItem = _cartItems.FirstOrDefault(item => item.ProductId == product.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                _cartItems.Add(new CartItemDto
                {
                    ProductId = product.Id,
                    ProductName = product.Name,
                    ProductImageUrl = product.ImageUrl,
                    UnitPrice = product.Price,
                    Quantity = quantity
                });
            }

            await SaveCart();
        }

        public async Task UpdateQuantity(int productId, int quantity)
        {
            var item = _cartItems.FirstOrDefault(item => item.ProductId == productId);
            if (item != null)
            {
                if (quantity <= 0)
                {
                    _cartItems.Remove(item);
                }
                else
                {
                    item.Quantity = quantity;
                }
                await SaveCart();
            }
        }

        public async Task RemoveFromCart(int productId)
        {
            var item = _cartItems.FirstOrDefault(item => item.ProductId == productId);
            if (item != null)
            {
                _cartItems.Remove(item);
                await SaveCart();
            }
        }

        public async Task ClearCart()
        {
            _cartItems.Clear();
            await SaveCart();
        }

        public CreateOrderDto PrepareOrder(string shippingAddress)
        {
            return new CreateOrderDto
            {
                ShippingAddress = shippingAddress,
                OrderItems = _cartItems.Select(item => new CreateOrderItemDto
                {
                    ProductId = item.ProductId,
                    Quantity = item.Quantity
                }).ToList()
            };
        }

        private async Task SaveCart()
        {
            // Si l'utilisateur n'est pas connecté, ne pas sauvegarder le panier
            if (!_authService.IsAuthenticated)
            {
                NotifyCartChanged();
                return;
            }

            // Utiliser l'ID de l'utilisateur comme partie de la clé du panier
            string cartKey = $"cart_{_authService.CurrentUser.Id}";
            Console.WriteLine($"Saving cart for user ID: {_authService.CurrentUser.Id} with {_cartItems.Count} items");

            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", cartKey, JsonSerializer.Serialize(_cartItems));
            NotifyCartChanged();
        }

        private void NotifyCartChanged() => OnCartChanged?.Invoke();
    }
}
