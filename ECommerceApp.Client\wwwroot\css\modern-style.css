/* Modern Style CSS for E-Shop Application */

:root {
    /* Primary Colors - Bleu Ciel */
    --primary-color: #48cae4;
    --primary-light: #90e0ef;
    --primary-dark: #00b4d8;

    /* Secondary Colors - Bleu Profond */
    --secondary-color: #0096c7;
    --secondary-light: #48cae4;
    --secondary-dark: #0077b6;

    /* Accent Colors - Bleu Vif */
    --accent-color: #03045e;
    --accent-light: #023e8a;
    --accent-dark: #03045e;

    /* Neutral Colors */
    --neutral-100: #f8f9fa;
    --neutral-200: #e9ecef;
    --neutral-300: #dee2e6;
    --neutral-400: #ced4da;
    --neutral-500: #adb5bd;
    --neutral-600: #6c757d;
    --neutral-700: #495057;
    --neutral-800: #343a40;
    --neutral-900: #212529;

    /* Success, Warning, Danger */
    --success-color: #38b000;
    --warning-color: #ffbe0b;
    --danger-color: #d90429;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Global Styles */
body {
    font-family: 'Inter', 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--neutral-800);
    background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 50%, #b3e5fc 100%);
    background-attachment: fixed;
    line-height: 1.6;
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--neutral-900);
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: 800;
    letter-spacing: -0.02em;
}

/* Buttons */
.btn {
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    text-transform: none;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 1;
}

.btn:hover::before {
    transform: translateX(0);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    border: none;
    color: white;
}

.btn-primary:hover, .btn-primary:focus {
    background: linear-gradient(to right, var(--primary-dark), var(--secondary-dark));
    border: none;
}

.btn-secondary {
    background: linear-gradient(to right, var(--secondary-color), var(--secondary-dark));
    border: none;
    color: white;
}

.btn-secondary:hover, .btn-secondary:focus {
    background: linear-gradient(to right, var(--secondary-dark), var(--accent-dark));
    border: none;
}

.btn-accent {
    background: linear-gradient(to right, var(--accent-color), var(--accent-dark));
    border: none;
    color: white;
}

.btn-accent:hover, .btn-accent:focus {
    background: linear-gradient(to right, var(--accent-dark), var(--accent-color));
    border: none;
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    border-color: transparent;
    color: white;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    overflow: hidden;
    background: linear-gradient(to bottom, #ffffff, #f8fdff);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 180, 216, 0.1);
}

.card-header {
    background: linear-gradient(to right, var(--primary-light), var(--primary-color));
    color: white;
    border-bottom: none;
    padding: var(--spacing-lg);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: white;
    border-top: 1px solid var(--neutral-200);
    padding: var(--spacing-lg);
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius-md);
    padding: 0.6rem 1rem;
    border: 1px solid var(--neutral-300);
    transition: all var(--transition-fast);
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(72, 202, 228, 0.25);
    background-color: white;
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--secondary-dark);
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Navigation */
.navbar {
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 50%, var(--primary-dark) 100%);
}

.navbar-brand {
    font-weight: 800;
    font-size: var(--font-size-xl);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-link {
    font-weight: 600;
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.nav-link:hover, .nav-link:focus {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

/* Tables */
.table {
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.table thead th {
    background-color: var(--neutral-100);
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.05em;
    padding: var(--spacing-md) var(--spacing-lg);
}

.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

/* Badges */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius-sm);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Product Cards */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

.product-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-card .card-title {
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.product-card .card-text {
    flex: 1;
}

.product-card .price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

/* Utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-accent { background-color: var(--accent-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn var(--transition-normal);
}

/* Media Queries */
@media (max-width: 768px) {
    .card {
        margin-bottom: var(--spacing-lg);
    }

    .navbar {
        padding: var(--spacing-sm) 0;
    }
}
