is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = browser
build_property.RootNamespace = ECommerceApp.Client
build_property.RootNamespace = ECommerceApp.Client
build_property.ProjectDir = C:\Users\<USER>\Desktop\DSI23 semester 2\projet .net\projet4\ECommerceApp.Client\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\DSI23 semester 2\projet .net\projet4\ECommerceApp.Client
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/AdminLogin.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5Mb2dpbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Admin/AdminComplaints.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cQWRtaW5Db21wbGFpbnRzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Admin/AdminDashboard.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cQWRtaW5EYXNoYm9hcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Admin/AdminProducts.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cQWRtaW5Qcm9kdWN0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Admin/AdminUsers.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWRtaW5cQWRtaW5Vc2Vycy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Cart.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ2FydC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/ComplaintDetail.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29tcGxhaW50RGV0YWlsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/ComplaintMessages.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29tcGxhaW50TWVzc2FnZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Complaints.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29tcGxhaW50cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ291bnRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/CreateComplaint.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ3JlYXRlQ29tcGxhaW50LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSG9tZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Landing.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTGFuZGluZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Login.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/OrderDetail.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcT3JkZXJEZXRhaWwucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Orders.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcT3JkZXJzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/ProductDetail.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZHVjdERldGFpbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Products.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZHVjdHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Profile.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUHJvZmlsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Register.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcUmVnaXN0ZXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/TestComplaint.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVGVzdENvbXBsYWludC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2VhdGhlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Shared/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/DSI23 semester 2/projet .net/projet4/ECommerceApp.Client/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = TGF5b3V0XE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-j2hp4d7pob
