﻿@page "/home"
@inject ProductService ProductService
@inject NavigationManager NavigationManager
@inject AuthService AuthService

<PageTitle>Accueil - E-Shop</PageTitle>

<!-- Hero Section -->
<div class="hero-section py-5 mb-5 text-white" style="background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 50%, var(--primary-light) 100%); border-radius: 0 0 2rem 2rem; box-shadow: var(--shadow-lg);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.2);">Bienvenue sur E-Shop</h1>
                <p class="lead mb-4">Découvrez notre sélection de produits électroniques de haute qualité à des prix compétitifs. Livraison rapide et service client exceptionnel.</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    <a href="products" class="btn btn-light btn-lg px-4 me-md-2 shadow-sm">
                        <i class="bi bi-grid-3x3-gap-fill me-2"></i>Découvrir nos produits
                    </a>
                    @if (!AuthService.IsAuthenticated)
                    {
                        <a href="register" class="btn btn-outline-light btn-lg px-4">
                            <i class="bi bi-person-plus-fill me-2"></i>S'inscrire
                        </a>
                    }
                </div>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <img src="https://picsum.photos/600/400" alt="E-Shop Electronics" class="img-fluid rounded-lg shadow-lg transform-hover"
                     style="border-radius: 1rem; transition: transform 0.3s ease; border: 5px solid rgba(255,255,255,0.2);"
                     onerror="this.onerror=null; this.src='https://dummyimage.com/600x400/48cae4/ffffff&text=E-Shop';">
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container mb-5">
    <div class="row g-4 py-4">
        <div class="col-md-4">
            <div class="feature-card text-center p-4 rounded-lg shadow h-100"
                 style="background: linear-gradient(to bottom right, #ffffff, #f0f9ff); border-radius: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div class="feature-icon mb-3">
                    <div class="icon-circle mx-auto d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-light), var(--primary-color)); border-radius: 50%; box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                        <i class="bi bi-truck text-white" style="font-size: 2rem;"></i>
                    </div>
                </div>
                <h3 class="h5 fw-bold">Livraison Rapide</h3>
                <p class="mb-0 text-muted">Livraison gratuite pour toutes les commandes de plus de 100€</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="feature-card text-center p-4 rounded-lg shadow h-100"
                 style="background: linear-gradient(to bottom right, #ffffff, #f0f9ff); border-radius: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div class="feature-icon mb-3">
                    <div class="icon-circle mx-auto d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-light), var(--primary-color)); border-radius: 50%; box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                        <i class="bi bi-shield-check text-white" style="font-size: 2rem;"></i>
                    </div>
                </div>
                <h3 class="h5 fw-bold">Garantie Qualité</h3>
                <p class="mb-0 text-muted">Tous nos produits sont garantis pendant 2 ans minimum</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="feature-card text-center p-4 rounded-lg shadow h-100"
                 style="background: linear-gradient(to bottom right, #ffffff, #f0f9ff); border-radius: 1rem; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <div class="feature-icon mb-3">
                    <div class="icon-circle mx-auto d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-light), var(--primary-color)); border-radius: 50%; box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                        <i class="bi bi-headset text-white" style="font-size: 2rem;"></i>
                    </div>
                </div>
                <h3 class="h5 fw-bold">Support 24/7</h3>
                <p class="mb-0 text-muted">Notre équipe de support est disponible pour vous aider à tout moment</p>
            </div>
        </div>
    </div>
</div>

<!-- Popular Products Section -->
<div class="container mb-5">
    <div class="section-header text-center mb-5">
        <h2 class="display-6 fw-bold" style="background: linear-gradient(to right, var(--primary-dark), var(--primary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; display: inline-block;">Produits Populaires</h2>
        <div class="d-flex justify-content-center">
            <div style="width: 80px; height: 4px; background: linear-gradient(to right, var(--primary-dark), var(--primary-color)); border-radius: 2px; margin-bottom: 1rem;"></div>
        </div>
        <p class="text-muted">Découvrez nos produits les plus vendus</p>
    </div>

    @if (products == null)
    {
        <div class="d-flex justify-content-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (products.Count == 0)
    {
        <div class="alert alert-info text-center">
            <p class="mb-0">Aucun produit disponible pour le moment.</p>
        </div>
    }
    else
    {
        <div class="row">
            @foreach (var product in products.Take(3))
            {
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100 border-0 shadow" style="border-radius: 1rem; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;"
                         onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 30px rgba(0, 180, 216, 0.1)';"
                         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';">
                        <div class="position-relative">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name"
                                     style="height: 250px; object-fit: cover; transition: transform 0.5s ease;"
                                     onmouseover="this.style.transform='scale(1.05)'"
                                     onmouseout="this.style.transform='scale(1)'"
                                     onerror="this.onerror=null; this.src='https://picsum.photos/300/250?random=@product.Id';">
                            }
                            else
                            {
                                <img src="https://picsum.photos/300/250?random=@product.Id" class="card-img-top" alt="@product.Name"
                                     style="height: 250px; object-fit: cover; transition: transform 0.5s ease;"
                                     onmouseover="this.style.transform='scale(1.05)'"
                                     onmouseout="this.style.transform='scale(1)'">
                            }
                            <div class="product-badge position-absolute top-0 end-0 m-2 px-3 py-1 rounded-pill"
                                 style="background: linear-gradient(to right, var(--primary-color), var(--primary-dark)); color: white; font-weight: 600; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                <i class="bi bi-star-fill me-1"></i> Populaire
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fw-bold">@product.Name</h5>
                            <p class="card-text text-muted">@(product.Description.Length > 80 ? product.Description.Substring(0, 80) + "..." : product.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="price-tag fw-bold" style="color: var(--primary-dark); font-size: 1.2rem;">@product.Price.ToString("C")</span>
                                <button class="btn btn-primary" style="border-radius: 0.75rem; box-shadow: 0 2px 5px rgba(0,0,0,0.1);" @onclick="() => NavigateToProductDetail(product.Id)">
                                    <i class="bi bi-eye-fill me-1"></i> Voir détails
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="text-center mt-4">
            <a href="products" class="btn btn-primary btn-lg px-4 py-2" style="border-radius: 2rem; box-shadow: 0 4px 10px rgba(0,180,216,0.3); background: linear-gradient(to right, var(--primary-color), var(--primary-dark)); border: none;">
                <i class="bi bi-grid-3x3-gap-fill me-2"></i> Voir tous les produits
            </a>
        </div>
    }
</div>

<!-- Testimonials Section -->
<div class="py-5 mb-5" style="background: linear-gradient(135deg, rgba(144, 224, 239, 0.1) 0%, rgba(72, 202, 228, 0.2) 100%); border-radius: 2rem; margin-top: 3rem;">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="display-6 fw-bold" style="background: linear-gradient(to right, var(--primary-dark), var(--primary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; display: inline-block;">Ce que disent nos clients</h2>
            <div class="d-flex justify-content-center">
                <div style="width: 80px; height: 4px; background: linear-gradient(to right, var(--primary-dark), var(--primary-color)); border-radius: 2px; margin-bottom: 1rem;"></div>
            </div>
            <p class="text-muted">Découvrez les avis de nos clients satisfaits</p>
        </div>

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow" style="border-radius: 1rem; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 30px rgba(0, 180, 216, 0.1)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';">
                    <div class="card-body p-4">
                        <div class="d-flex mb-3">
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning"></i>
                        </div>
                        <div class="mb-4">
                            <i class="bi bi-quote fs-1 text-primary opacity-25"></i>
                        </div>
                        <p class="card-text">"Service client exceptionnel et produits de qualité. Je recommande vivement cette boutique pour tous vos besoins électroniques."</p>
                    </div>
                    <div class="card-footer bg-white border-0 p-4">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle text-white d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                                <span>SM</span>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-bold">Sarah Mansour</h6>
                                <small class="text-muted">Cliente fidèle</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow" style="border-radius: 1rem; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 30px rgba(0, 180, 216, 0.1)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';">
                    <div class="card-body p-4">
                        <div class="d-flex mb-3">
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning"></i>
                        </div>
                        <div class="mb-4">
                            <i class="bi bi-quote fs-1 text-primary opacity-25"></i>
                        </div>
                        <p class="card-text">"Livraison rapide et produits conformes à la description. Je suis très satisfait de mon achat et je reviendrai certainement."</p>
                    </div>
                    <div class="card-footer bg-white border-0 p-4">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle text-white d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                                <span>AB</span>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-bold">Ahmed Ben Ali</h6>
                                <small class="text-muted">Client satisfait</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow" style="border-radius: 1rem; overflow: hidden; transition: transform 0.3s ease, box-shadow 0.3s ease;"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 30px rgba(0, 180, 216, 0.1)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';">
                    <div class="card-body p-4">
                        <div class="d-flex mb-3">
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-fill text-warning me-1"></i>
                            <i class="bi bi-star-half text-warning"></i>
                        </div>
                        <div class="mb-4">
                            <i class="bi bi-quote fs-1 text-primary opacity-25"></i>
                        </div>
                        <p class="card-text">"Prix compétitifs et large gamme de produits. J'ai trouvé exactement ce que je cherchais. Merci E-Shop pour votre professionnalisme!"</p>
                    </div>
                    <div class="card-footer bg-white border-0 p-4">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle text-white d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); box-shadow: 0 4px 10px rgba(0,180,216,0.3);">
                                <span>LT</span>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-bold">Leila Trabelsi</h6>
                                <small class="text-muted">Cliente régulière</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Newsletter Section -->
<div class="container mb-5">
    <div class="card border-0 text-white shadow-lg" style="border-radius: 1.5rem; overflow: hidden; background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
        <div class="card-body p-5 position-relative" style="z-index: 1;">
            <!-- Decorative elements -->
            <div class="position-absolute" style="top: -30px; right: -30px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
            <div class="position-absolute" style="bottom: -20px; left: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>

            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <h3 class="fw-bold" style="text-shadow: 0 2px 4px rgba(0,0,0,0.1);">Abonnez-vous à notre newsletter</h3>
                    <p class="mb-0 opacity-90">Recevez nos dernières offres et promotions directement dans votre boîte mail.</p>
                </div>
                <div class="col-lg-6">
                    <div class="input-group">
                        <input type="email" class="form-control form-control-lg" placeholder="Votre adresse email" style="border-radius: 2rem 0 0 2rem; border: none; padding-left: 1.5rem;">
                        <button class="btn btn-light btn-lg px-4" type="button" style="border-radius: 0 2rem 2rem 0; font-weight: 600;">
                            <i class="bi bi-send-fill me-2"></i>S'abonner
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ProductDto> products;

    protected override async Task OnInitializedAsync()
    {
        var response = await ProductService.GetProducts();
        if (response.Success)
        {
            products = response.Data;
        }
        else
        {
            products = new List<ProductDto>();
        }
    }

    private void NavigateToProductDetail(int id)
    {
        NavigationManager.NavigateTo($"/products/{id}");
    }
}
