using System;
using System.ComponentModel.DataAnnotations;

namespace ECommerceApp.Shared.Models
{
    public class Product
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Le nom du produit est requis")]
        public string Name { get; set; }

        [Required(ErrorMessage = "La description est requise")]
        public string Description { get; set; }

        [Required(ErrorMessage = "Le prix est requis")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le prix doit être supérieur à 0")]
        public decimal Price { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Le stock ne peut pas être négatif")]
        public int StockQuantity { get; set; }

        public string? ImageUrl { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }

    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal Price { get; set; }
        public int StockQuantity { get; set; }
        public string? ImageUrl { get; set; }
    }

    public class ProductCreateDto
    {
        [Required(ErrorMessage = "Le nom du produit est requis")]
        public string Name { get; set; }

        [Required(ErrorMessage = "La description est requise")]
        public string Description { get; set; }

        [Required(ErrorMessage = "Le prix est requis")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le prix doit être supérieur à 0")]
        public decimal Price { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Le stock ne peut pas être négatif")]
        public int StockQuantity { get; set; }

        public string? ImageUrl { get; set; }
    }

    public class ProductUpdateDto
    {
        [Required(ErrorMessage = "Le nom du produit est requis")]
        public string Name { get; set; }

        [Required(ErrorMessage = "La description est requise")]
        public string Description { get; set; }

        [Required(ErrorMessage = "Le prix est requis")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le prix doit être supérieur à 0")]
        public decimal Price { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Le stock ne peut pas être négatif")]
        public int StockQuantity { get; set; }

        public string? ImageUrl { get; set; }
    }
}
