@page "/admin"
@attribute [Authorize(Roles = "Admin")]
@inject NavigationManager NavigationManager
@inject ProductService ProductService
@inject ComplaintService ComplaintService
@inject AuthService AuthService

<PageTitle>Tableau de bord Admin - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-4"><i class="oi oi-dashboard me-2"></i>Tableau de bord Administrateur</h1>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center py-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <!-- Statistiques -->
            <div class="col-md-12 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Statistiques</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">Produits</h6>
                                                <h2 class="mb-0">@productCount</h2>
                                            </div>
                                            <i class="oi oi-grid-three-up" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">Réclamations</h6>
                                                <h2 class="mb-0">@complaintCount</h2>
                                            </div>
                                            <i class="oi oi-comment-square" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">Réclamations en attente</h6>
                                                <h2 class="mb-0">@pendingComplaintCount</h2>
                                            </div>
                                            <i class="oi oi-clock" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accès rapide -->
            <div class="col-md-12 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Accès rapide</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="oi oi-grid-three-up text-primary mb-3" style="font-size: 3rem;"></i>
                                        <h5>Gestion des Produits</h5>
                                        <p class="text-muted">Ajouter, modifier ou supprimer des produits</p>
                                        <a href="/admin/products" class="btn btn-primary">Accéder</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="oi oi-comment-square text-warning mb-3" style="font-size: 3rem;"></i>
                                        <h5>Gestion des Réclamations</h5>
                                        <p class="text-muted">Répondre aux réclamations des clients</p>
                                        <a href="/admin/complaints" class="btn btn-warning">Accéder</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="oi oi-people text-success mb-3" style="font-size: 3rem;"></i>
                                        <h5>Gestion des Clients</h5>
                                        <p class="text-muted">Consulter les informations des clients</p>
                                        <a href="/admin/users" class="btn btn-success">Accéder</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool isLoading = true;
    private int productCount = 0;
    private int complaintCount = 0;
    private int pendingComplaintCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;

            // Charger les produits
            var productsResponse = await ProductService.GetProducts();
            if (productsResponse.Success && productsResponse.Data != null)
            {
                productCount = productsResponse.Data.Count;
            }

            // Charger les réclamations
            var complaintsResponse = await ComplaintService.GetComplaints();
            if (complaintsResponse.Success && complaintsResponse.Data != null)
            {
                complaintCount = complaintsResponse.Data.Count;
                pendingComplaintCount = complaintsResponse.Data.Count(c => c.Status == "Ouvert");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données du tableau de bord: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
