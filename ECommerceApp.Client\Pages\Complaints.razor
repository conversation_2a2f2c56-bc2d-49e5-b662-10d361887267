@page "/complaints"
@attribute [Authorize]
@inject ComplaintService ComplaintService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Mes Réclamations - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Accueil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Mes Réclamations</li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 fw-bold mb-2"><i class="oi oi-warning me-2"></i>Mes Réclamations</h1>
                    <p class="lead">Consultez et suivez l'état de vos réclamations concernant les produits achetés.</p>
                </div>
                <button class="btn btn-primary" @onclick="() => showNewComplaintForm = true">
                    <i class="oi oi-plus me-2"></i> Nouvelle réclamation
                </button>
            </div>
        </div>
    </div>

@if (complaints == null)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else if (complaints.Count == 0)
{
    <div class="alert alert-info p-4 shadow-sm">
        <h4 class="alert-heading mb-3"><i class="oi oi-info me-2"></i>Aucune réclamation</h4>
        <p>Vous n'avez pas encore soumis de réclamation.</p>
        <hr>
        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
            <a href="/orders" class="btn btn-primary">
                <i class="oi oi-list me-2"></i> Voir mes commandes
            </a>
        </div>
    </div>
}
else
{
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="ps-4">N° Réclamation</th>
                            <th>Sujet</th>
                            <th>Date</th>
                            <th>Statut</th>
                            <th class="text-end pe-4">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var complaint in complaints)
                        {
                            <tr>
                                <td class="ps-4 fw-bold">#@complaint.Id</td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 300px;" title="@complaint.Subject">
                                        @complaint.Subject
                                    </span>
                                </td>
                                <td>@complaint.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>
                                    @switch (complaint.Status)
                                    {
                                        case "Ouvert":
                                            <span class="badge bg-warning text-dark">
                                                <i class="oi oi-clock me-1"></i> @complaint.Status
                                            </span>
                                            break;
                                        case "En cours":
                                            <span class="badge bg-info">
                                                <i class="oi oi-loop-circular me-1"></i> @complaint.Status
                                            </span>
                                            break;
                                        case "Résolu":
                                            <span class="badge bg-success">
                                                <i class="oi oi-check me-1"></i> @complaint.Status
                                            </span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">
                                                <i class="oi oi-question-mark me-1"></i> @complaint.Status
                                            </span>
                                            break;
                                    }
                                </td>
                                <td class="text-end pe-4">
                                    <button class="btn btn-sm btn-primary" @onclick="() => ViewComplaintDetails(complaint.Id)">
                                        <i class="oi oi-eye me-1"></i> Détails
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

@if (showNewComplaintForm)
{
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-primary text-white py-3">
            <h3 class="mb-0"><i class="oi oi-plus me-2"></i>Nouvelle réclamation</h3>
        </div>
        <div class="card-body p-4">
            <EditForm Model="@newComplaint" OnValidSubmit="CreateComplaint">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger mb-4" />

                <div class="mb-3">
                    <label for="subject" class="form-label">Sujet de la réclamation</label>
                    <InputText id="subject" class="form-control" @bind-Value="newComplaint.Subject" placeholder="Ex: Produit défectueux, Livraison en retard, etc." />
                    <ValidationMessage For="@(() => newComplaint.Subject)" class="text-danger" />
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description détaillée</label>
                    <InputTextArea id="description" class="form-control" @bind-Value="newComplaint.Description" rows="6" placeholder="Décrivez en détail le problème rencontré..." />
                    <ValidationMessage For="@(() => newComplaint.Description)" class="text-danger" />
                    <div class="form-text">Soyez précis et incluez toutes les informations pertinentes pour nous aider à traiter votre réclamation.</div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary" @onclick="CancelNewComplaint">
                        <i class="oi oi-x me-2"></i> Annuler
                    </button>
                    <button type="submit" class="btn btn-primary" disabled="@isProcessing">
                        @if (isProcessing)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            <span>Envoi en cours...</span>
                        }
                        else
                        {
                            <i class="oi oi-check me-2"></i>
                            <span>Envoyer la réclamation</span>
                        }
                    </button>
                </div>
            </EditForm>
        </div>
    </div>
}
</div>

@code {
    private List<ComplaintDto> complaints;
    private CreateComplaintDto newComplaint = new CreateComplaintDto();
    private bool showNewComplaintForm = false;
    private bool isProcessing = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadComplaints();
    }

    private async Task LoadComplaints()
    {
        var response = await ComplaintService.GetComplaints();
        if (response.Success)
        {
            complaints = response.Data;
        }
        else
        {
            complaints = new List<ComplaintDto>();
        }
    }

    private void ViewComplaintDetails(int id)
    {
        NavigationManager.NavigateTo($"/complaints/{id}");
    }

    private void CancelNewComplaint()
    {
        showNewComplaintForm = false;
        newComplaint = new CreateComplaintDto();
    }

    private async Task CreateComplaint()
    {
        isProcessing = true;

        try
        {
            if (newComplaint.ProductId <= 0)
            {
                
                newComplaint.ProductId = 1; 
            }

            Console.WriteLine($"Envoi de la réclamation pour le produit ID: {newComplaint.ProductId}");

            var response = await ComplaintService.CreateComplaint(newComplaint);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Votre réclamation a été soumise avec succès ! Nous la traiterons dans les plus brefs délais.");
                showNewComplaintForm = false;
                newComplaint = new CreateComplaintDto();
                await LoadComplaints();
            }
            else
            {
                Console.WriteLine($"Erreur lors de la création de la réclamation: {response.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la création de la réclamation: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Une erreur s'est produite: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }
}
