@page "/landing"
@inject NavigationManager NavigationManager

<PageTitle>Bienvenue - E-Shop</PageTitle>

<div class="landing-page">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10 text-center">
                <h1 class="display-3 fw-bold mb-4">Bienvenue sur E-Shop</h1>
                <p class="lead mb-5">Choisissez votre mode d'accès à la plateforme</p>
            </div>
        </div>

        <div class="row justify-content-center mb-5">
            <div class="col-md-5">
                <div class="card shadow-lg h-100">
                    <div class="card-body text-center p-5">
                        <i class="oi oi-person display-1 text-primary mb-4"></i>
                        <h2 class="card-title mb-4">Espace Client</h2>
                        <p class="card-text mb-4">Accédez à notre boutique en ligne pour découvrir nos produits, passer des commandes et suivre vos achats.</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg mb-2" @onclick="NavigateToLogin">
                                <i class="oi oi-account-login me-2"></i> Connexion
                            </button>
                            <button class="btn btn-outline-primary btn-lg" @onclick="NavigateToRegister">
                                <i class="oi oi-plus me-2"></i> Inscription
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-5">
                <div class="card shadow-lg h-100">
                    <div class="card-body text-center p-5">
                        <i class="oi oi-lock-locked display-1 text-danger mb-4"></i>
                        <h2 class="card-title mb-4">Administration</h2>
                        <p class="card-text mb-4">Espace réservé aux administrateurs pour gérer les produits, les commandes et les utilisateurs.</p>
                        <button class="btn btn-danger btn-lg px-5" @onclick="NavigateToAdminLogin">
                            <i class="oi oi-key me-2"></i> Connexion administrateur
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .landing-page {
        min-height: calc(100vh - 160px);
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
    }

    .card {
        border-radius: 15px;
        transition: transform 0.3s ease;
    }

    .card:hover {
        transform: translateY(-10px);
    }
</style>

@code {
    private void NavigateToClientHome()
    {
        NavigationManager.NavigateTo("/home");
    }

    private void NavigateToLogin()
    {
        NavigationManager.NavigateTo("/login");
    }

    private void NavigateToRegister()
    {
        NavigationManager.NavigateTo("/register");
    }

    private void NavigateToAdminLogin()
    {
        NavigationManager.NavigateTo("/admin-login");
    }
}
