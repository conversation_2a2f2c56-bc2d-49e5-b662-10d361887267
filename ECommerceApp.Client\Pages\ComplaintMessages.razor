@page "/complaint-messages"
@attribute [Authorize]
@inject ComplaintService ComplaintService
@inject ProductService ProductService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Mes Messages de Réclamation - E-Shop</PageTitle>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-0"><i class="oi oi-envelope-closed me-2"></i>Mes Messages de Réclamation</h1>
            <p class="text-muted mt-2">Suivez vos réclamations et les réponses de l'administration</p>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center py-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (complaints == null || complaints.Count == 0)
    {
        <div class="alert alert-info">
            <p class="mb-0">Vous n'avez pas encore de réclamations.</p>
            <a href="/products" class="btn btn-primary mt-3">Parcourir les produits</a>
        </div>
    }
    else
    {
        <div class="row mb-4">
            <div class="col-md-4 mb-3 mb-md-0">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Mes Réclamations</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            @foreach (var complaint in complaints)
                            {
                                <button type="button"
                                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center @(selectedComplaint?.Id == complaint.Id ? "active" : "")"
                                        @onclick="() => SelectComplaint(complaint)">
                                    <div>
                                        <div class="fw-bold text-truncate" style="max-width: 200px;">@complaint.Subject</div>
                                        <small class="text-muted">@complaint.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                    </div>
                                    <span class="badge @GetStatusBadgeClass(complaint.Status)">@complaint.Status</span>
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                @if (selectedComplaint != null)
                {
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">@selectedComplaint.Subject</h5>
                            <span class="badge @GetStatusBadgeClass(selectedComplaint.Status)">@selectedComplaint.Status</span>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <small class="text-muted">Créée le @selectedComplaint.CreatedAt.ToString("dd/MM/yyyy à HH:mm")</small>
                                    <small class="text-muted">Produit: @productName</small>
                                </div>
                                <div class="p-3 bg-light rounded">
                                    <p class="mb-0">@selectedComplaint.Description</p>
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(selectedComplaint.AdminResponse))
                            {
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between mb-2">
                                        <h6 class="mb-0">Réponse de l'administration</h6>
                                        <small class="text-muted">@selectedComplaint.ResponseDate?.ToString("dd/MM/yyyy à HH:mm")</small>
                                    </div>
                                    <div class="p-3 bg-success bg-opacity-10 rounded border border-success">
                                        <p class="mb-0">@selectedComplaint.AdminResponse</p>
                                    </div>
                                </div>

                                @if (selectedComplaint.Status != "Résolu")
                                {
                                    <div class="d-flex justify-content-end">
                                        <button class="btn btn-success" @onclick="() => MarkAsResolved(selectedComplaint.Id)">
                                            <i class="oi oi-check me-2"></i>Marquer comme résolu
                                        </button>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="alert alert-info">
                                    <p class="mb-0">En attente d'une réponse de l'administration...</p>
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="oi oi-envelope-closed text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">Sélectionnez une réclamation</h5>
                            <p class="text-muted">Cliquez sur une réclamation dans la liste pour voir les détails et les réponses</p>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    private List<ComplaintDto> complaints;
    private ComplaintDto selectedComplaint;
    private string productName;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadComplaints();
    }

    private async Task LoadComplaints()
    {
        try
        {
            isLoading = true;
            var response = await ComplaintService.GetComplaints();
            if (response.Success)
            {
                complaints = response.Data;
                // Trier les réclamations par date (les plus récentes en premier)
                complaints = complaints.OrderByDescending(c => c.CreatedAt).ToList();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SelectComplaint(ComplaintDto complaint)
    {
        selectedComplaint = complaint;

        // Charger les informations du produit
        try
        {
            var productResponse = await ProductService.GetProduct(complaint.ProductId);
            if (productResponse.Success && productResponse.Data != null)
            {
                productName = productResponse.Data.Name;
            }
            else
            {
                productName = "Produit non trouvé";
            }
        }
        catch
        {
            productName = "Produit non disponible";
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Ouvert" => "bg-danger",
            "En cours" => "bg-warning",
            "Résolu" => "bg-success",
            _ => "bg-secondary"
        };
    }

    private async Task MarkAsResolved(int complaintId)
    {
        try
        {
            var updateModel = new UpdateComplaintDto
            {
                Status = "Résolu",
                AdminResponse = selectedComplaint.AdminResponse
            };

            var response = await ComplaintService.UpdateComplaintStatus(complaintId, updateModel);
            if (response.Success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Réclamation marquée comme résolue !");
                await LoadComplaints();

                // Sélectionner à nouveau la réclamation mise à jour
                selectedComplaint = complaints.FirstOrDefault(c => c.Id == complaintId);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {ex.Message}");
        }
    }
}
