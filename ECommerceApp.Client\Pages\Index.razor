@page "/"
@inject NavigationManager NavigationManager
@inject AuthService AuthService

<PageTitle>Bienvenue - E-Shop</PageTitle>

<div class="landing-page">
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-3 fw-bold mb-3">E-Shop</h1>
            <p class="lead mb-4">Votre boutique en ligne pour tous vos besoins électroniques</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-0">
                        <div class="row g-0">
                            <!-- Section Client -->
                            <div class="col-md-6 p-5 bg-primary text-white">
                                <div class="text-center mb-4">
                                    <i class="oi oi-person display-1 mb-3"></i>
                                    <h2 class="fw-bold">Espace Client</h2>
                                    <p>Accédez à votre compte client pour faire vos achats et suivre vos commandes.</p>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="/login" class="btn btn-light btn-lg">
                                        <i class="oi oi-account-login me-2"></i>Connexion
                                    </a>
                                    <a href="/register" class="btn btn-outline-light btn-lg">
                                        <i class="oi oi-plus me-2"></i>Inscription
                                    </a>
                                    <a href="/home" class="btn btn-outline-light">
                                        <i class="oi oi-home me-2"></i>Accéder à la boutique
                                    </a>
                                </div>
                            </div>

                            <!-- Section Admin -->
                            <div class="col-md-6 p-5 bg-dark text-white">
                                <div class="text-center mb-4">
                                    <i class="oi oi-shield display-1 mb-3"></i>
                                    <h2 class="fw-bold">Espace Administration</h2>
                                    <p>Réservé aux administrateurs pour la gestion de la boutique.</p>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="/admin-login" class="btn btn-danger btn-lg">
                                        <i class="oi oi-lock-locked me-2"></i>Connexion Admin
                                    </a>
                                </div>
                                <div class="mt-3 text-center">
                                    <small class="text-muted">Email: <EMAIL></small><br>
                                    <small class="text-muted">Mot de passe: admin123</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <p class="text-muted">© 2023 E-Shop. Tous droits réservés.</p>
        </div>
    </div>
</div>

<style>
    .landing-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
</style>

@code {
    protected override void OnInitialized()
    {
        // Si l'utilisateur est déjà connecté, le rediriger vers la page appropriée
        if (AuthService.IsAuthenticated)
        {
            if (AuthService.IsAdmin)
            {
                NavigationManager.NavigateTo("/admin");
            }
            else
            {
                NavigationManager.NavigateTo("/home");
            }
        }
    }
}
