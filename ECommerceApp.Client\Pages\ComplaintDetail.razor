@page "/complaints/{Id:int}"
@attribute [Authorize]
@inject ComplaintService ComplaintService
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Détail de la réclamation - E-Commerce App</PageTitle>

@if (complaint == null)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Réclamation #@complaint.Id</h1>
        <a href="/complaints" class="btn btn-outline-primary">
            <i class="oi oi-arrow-left"></i> Retour aux réclamations
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">@complaint.Subject</h5>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <p class="text-muted">
                    <small>
                        Soumis par @complaint.Username le @complaint.CreatedAt.ToString("dd/MM/yyyy à HH:mm")
                    </small>
                </p>
                <p class="mb-0">
                    <span class="badge @GetStatusBadgeClass(complaint.Status)">@complaint.Status</span>
                </p>
            </div>
            <div class="card bg-light p-3 mb-4">
                <h6 class="card-subtitle mb-2 text-muted">Description</h6>
                <p class="card-text">@complaint.Description</p>
            </div>

            @if (!string.IsNullOrEmpty(complaint.AdminResponse))
            {
                <div class="card bg-light p-3">
                    <h6 class="card-subtitle mb-2 text-muted">Réponse de l'administrateur (@complaint.ResponseDate?.ToString("dd/MM/yyyy à HH:mm"))</h6>
                    <p class="card-text">@complaint.AdminResponse</p>
                </div>
            }
        </div>
    </div>

    @if (AuthService.IsAdmin && (complaint.Status != "Résolu"))
    {
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Répondre à la réclamation</h5>
            </div>
            <div class="card-body">
                <EditForm Model="@responseModel" OnValidSubmit="RespondToComplaint">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="status" class="form-label">Statut</label>
                        <InputSelect id="status" class="form-select" @bind-Value="responseModel.Status">
                            <option value="En cours">En cours</option>
                            <option value="Résolu">Résolu</option>
                        </InputSelect>
                        <ValidationMessage For="@(() => responseModel.Status)" />
                    </div>

                    <div class="mb-3">
                        <label for="adminResponse" class="form-label">Réponse</label>
                        <InputTextArea id="adminResponse" class="form-control" @bind-Value="responseModel.AdminResponse" rows="5" />
                        <ValidationMessage For="@(() => responseModel.AdminResponse)" />
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" disabled="@isProcessing">
                            @if (isProcessing)
                            {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span> Envoi en cours...</span>
                            }
                            else
                            {
                                <span>Envoyer la réponse</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    }
}

@code {
    [Parameter]
    public int Id { get; set; }

    private ComplaintDto complaint;
    private UpdateComplaintDto responseModel = new UpdateComplaintDto();
    private bool isProcessing = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadComplaint();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadComplaint();
    }

    private async Task LoadComplaint()
    {
        var response = await ComplaintService.GetComplaint(Id);
        if (response.Success)
        {
            complaint = response.Data;
            
            if (AuthService.IsAdmin)
            {
                responseModel.Status = string.IsNullOrEmpty(complaint.AdminResponse) ? "En cours" : complaint.Status;
                responseModel.AdminResponse = complaint.AdminResponse ?? "";
            }
        }
        else
        {
            NavigationManager.NavigateTo("/complaints");
            await JSRuntime.InvokeVoidAsync("alert", "Réclamation non trouvée.");
        }
    }

    private async Task RespondToComplaint()
    {
        if (!AuthService.IsAdmin)
        {
            return;
        }

        isProcessing = true;

        try
        {
            var response = await ComplaintService.RespondToComplaint(Id, responseModel);
            if (response.Success)
            {
                complaint = response.Data;
                await JSRuntime.InvokeVoidAsync("alert", "Réponse envoyée avec succès.");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Erreur: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Une erreur s'est produite: {ex.Message}");
        }
        finally
        {
            isProcessing = false;
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Ouvert" => "bg-warning text-dark",
            "En cours" => "bg-info",
            "Résolu" => "bg-success",
            _ => "bg-secondary"
        };
    }
}
