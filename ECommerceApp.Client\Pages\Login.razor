@page "/login"
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Connexion - E-Commerce App</PageTitle>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Connexion</h3>
            </div>
            <div class="card-body">
                <EditForm Model="@model" OnValidSubmit="HandleLogin">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            @errorMessage
                        </div>
                    }

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <InputText id="email" class="form-control" @bind-Value="model.Email" />
                        <ValidationMessage For="@(() => model.Email)" />
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <InputText id="password" type="password" class="form-control" @bind-Value="model.Password" />
                        <ValidationMessage For="@(() => model.Password)" />
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" disabled="@isLoading">
                            @if (isLoading)
                            {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span> Connexion en cours...</span>
                            }
                            else
                            {
                                <span>Se connecter</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Vous n'avez pas de compte ? <a href="register">Inscrivez-vous</a></p>
            </div>
        </div>
    </div>
</div>

@code {
    private UserLoginDto model = new UserLoginDto();
    private string errorMessage;
    private bool isLoading;

    protected override void OnInitialized()
    {
        if (AuthService.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        Console.WriteLine("HandleLogin called");
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            Console.WriteLine("Calling AuthService.Login");
            var response = await AuthService.Login(model);
            Console.WriteLine("Login response received: " + response.Success);

            if (response.Success)
            {
                Console.WriteLine("Login successful, redirecting to home page");
                await JSRuntime.InvokeVoidAsync("console.log", "Login successful, redirecting to home page");

                Console.WriteLine("Redirecting to home page with full refresh");
                await JSRuntime.InvokeVoidAsync("console.log", "Redirecting to home page with full refresh");

                await Task.Delay(500);

                NavigationManager.NavigateTo("/", true);
            }
            else
            {
                Console.WriteLine("Login failed: " + response.Message);
                errorMessage = response.Message;
                if (response.Errors != null && response.Errors.Any())
                {
                    errorMessage = string.Join(", ", response.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Login exception: " + ex.Message);
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
