using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using ECommerceApp.Client;
using ECommerceApp.Client.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configuration de l'URL de base de l'API
var apiUrl = builder.Configuration.GetValue<string>("ApiUrl") ?? builder.HostEnvironment.BaseAddress;
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(apiUrl) });

// Enregistrement des services
builder.Services.AddScoped<HttpService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<ProductService>();
builder.Services.AddScoped<OrderService>();
builder.Services.AddScoped<ComplaintService>();
builder.Services.AddScoped<CartService>();

// Ajout de l'authentification
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthStateProvider>();

var host = builder.Build();

// Initialisation des services
try
{
    var authService = host.Services.GetRequiredService<AuthService>();
    await authService.Initialize();

    var cartService = host.Services.GetRequiredService<CartService>();
    await cartService.Initialize();
}
catch (Exception ex)
{
    Console.WriteLine($"Erreur lors de l'initialisation des services: {ex.Message}");
}

await host.RunAsync();
