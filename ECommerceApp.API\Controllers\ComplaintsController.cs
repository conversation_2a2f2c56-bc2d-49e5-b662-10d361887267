using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ECommerceApp.API.Data;
using ECommerceApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace ECommerceApp.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ComplaintsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ComplaintsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Complaints
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<ComplaintDto>>>> GetComplaints()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            var isAdmin = User.IsInRole("Admin");

            IQueryable<Complaint> query = _context.Complaints
                .Include(c => c.User);

            
            if (!isAdmin)
            {
                query = query.Where(c => c.UserId == userId);
            }

            var complaints = await query.ToListAsync();

            var complaintDtos = complaints.Select(c => new ComplaintDto
            {
                Id = c.Id,
                UserId = c.UserId,
                ProductId = c.ProductId,
                Username = c.User.Username,
                Subject = c.Subject,
                Description = c.Description,
                CreatedAt = c.CreatedAt,
                Status = c.Status,
                AdminResponse = c.AdminResponse,
                ResponseDate = c.ResponseDate
            }).ToList();

            return Ok(ApiResponse<List<ComplaintDto>>.SuccessResponse(complaintDtos));
        }

        // GET: api/Complaints/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<ComplaintDto>>> GetComplaint(int id)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            var isAdmin = User.IsInRole("Admin");

            var complaint = await _context.Complaints
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (complaint == null)
            {
                return NotFound(ApiResponse<ComplaintDto>.ErrorResponse("Réclamation non trouvée"));
            }

            
            if (!isAdmin && complaint.UserId != userId)
            {
                return Forbid();
            }

            var complaintDto = new ComplaintDto
            {
                Id = complaint.Id,
                UserId = complaint.UserId,
                ProductId = complaint.ProductId,
                Username = complaint.User.Username,
                Subject = complaint.Subject,
                Description = complaint.Description,
                CreatedAt = complaint.CreatedAt,
                Status = complaint.Status,
                AdminResponse = complaint.AdminResponse,
                ResponseDate = complaint.ResponseDate
            };

            return Ok(ApiResponse<ComplaintDto>.SuccessResponse(complaintDto));
        }

        // POST: api/Complaints
        [HttpPost]
        public async Task<ActionResult<ApiResponse<ComplaintDto>>> CreateComplaint(CreateComplaintDto model)
        {
            try
            {
                
                Console.WriteLine($"Données reçues: ProductId={model.ProductId}, Subject={model.Subject}, Description={model.Description}");

                if (!ModelState.IsValid)
                {
                    var errors = string.Join("; ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage));
                    Console.WriteLine($"Validation errors: {errors}");
                    return BadRequest(ApiResponse<ComplaintDto>.ErrorResponse($"Données invalides: {errors}"));
                }

                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (userId == 0)
                {
                    return Unauthorized(ApiResponse<ComplaintDto>.ErrorResponse("Utilisateur non authentifié"));
                }

                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(ApiResponse<ComplaintDto>.ErrorResponse("Utilisateur non trouvé"));
                }

                
                var product = await _context.Products.FindAsync(model.ProductId);
                if (product == null)
                {
                    return BadRequest(ApiResponse<ComplaintDto>.ErrorResponse($"Produit avec ID {model.ProductId} non trouvé"));
                }

                var complaint = new Complaint
                {
                    UserId = userId,
                    ProductId = model.ProductId,
                    Subject = model.Subject ?? "Sans sujet",
                    Description = model.Description ?? "Sans description",
                    CreatedAt = DateTime.Now,
                    Status = "Ouvert"
                };

                _context.Complaints.Add(complaint);
                await _context.SaveChangesAsync();

                var complaintDto = new ComplaintDto
                {
                    Id = complaint.Id,
                    UserId = complaint.UserId,
                    ProductId = complaint.ProductId,
                    Username = user.Username,
                    Subject = complaint.Subject,
                    Description = complaint.Description,
                    CreatedAt = complaint.CreatedAt,
                    Status = complaint.Status,
                    AdminResponse = complaint.AdminResponse,
                    ResponseDate = complaint.ResponseDate
                };

                return CreatedAtAction(nameof(GetComplaint), new { id = complaint.Id }, ApiResponse<ComplaintDto>.SuccessResponse(complaintDto, "Réclamation créée avec succès"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception dans CreateComplaint: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, ApiResponse<ComplaintDto>.ErrorResponse($"Une erreur s'est produite: {ex.Message}"));
            }
        }

        // PUT: api/Complaints/5/response
        [HttpPut("{id}/response")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse<ComplaintDto>>> RespondToComplaint(int id, UpdateComplaintDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<ComplaintDto>.ErrorResponse("Données invalides", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }

            var complaint = await _context.Complaints
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (complaint == null)
            {
                return NotFound(ApiResponse<ComplaintDto>.ErrorResponse("Réclamation non trouvée"));
            }

            
            complaint.Status = model.Status;
            complaint.AdminResponse = model.AdminResponse;
            complaint.ResponseDate = DateTime.Now;

            _context.Entry(complaint).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            var complaintDto = new ComplaintDto
            {
                Id = complaint.Id,
                UserId = complaint.UserId,
                ProductId = complaint.ProductId,
                Username = complaint.User.Username,
                Subject = complaint.Subject,
                Description = complaint.Description,
                CreatedAt = complaint.CreatedAt,
                Status = complaint.Status,
                AdminResponse = complaint.AdminResponse,
                ResponseDate = complaint.ResponseDate
            };

            return Ok(ApiResponse<ComplaintDto>.SuccessResponse(complaintDto, "Réponse à la réclamation envoyée avec succès"));
        }

        // PUT: api/Complaints/5/status
        [HttpPut("{id}/status")]
        public async Task<ActionResult<ApiResponse<ComplaintDto>>> UpdateComplaintStatus(int id, UpdateComplaintDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<ComplaintDto>.ErrorResponse("Données invalides", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }

            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            if (userId == 0)
            {
                return Unauthorized(ApiResponse<ComplaintDto>.ErrorResponse("Utilisateur non authentifié"));
            }

            var complaint = await _context.Complaints
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (complaint == null)
            {
                return NotFound(ApiResponse<ComplaintDto>.ErrorResponse("Réclamation non trouvée"));
            }

            
            if (complaint.UserId != userId)
            {
                return Forbid();
            }

            
            complaint.Status = model.Status;

            _context.Entry(complaint).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            var complaintDto = new ComplaintDto
            {
                Id = complaint.Id,
                UserId = complaint.UserId,
                ProductId = complaint.ProductId,
                Username = complaint.User.Username,
                Subject = complaint.Subject,
                Description = complaint.Description,
                CreatedAt = complaint.CreatedAt,
                Status = complaint.Status,
                AdminResponse = complaint.AdminResponse,
                ResponseDate = complaint.ResponseDate
            };

            return Ok(ApiResponse<ComplaintDto>.SuccessResponse(complaintDto, "Statut de la réclamation mis à jour avec succès"));
        }

        // PUT: api/Complaints/5
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse<ComplaintDto>>> UpdateComplaint(int id, UpdateComplaintDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<ComplaintDto>.ErrorResponse("Données invalides", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }

            var complaint = await _context.Complaints
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (complaint == null)
            {
                return NotFound(ApiResponse<ComplaintDto>.ErrorResponse("Réclamation non trouvée"));
            }

            
            complaint.Status = model.Status;
            complaint.AdminResponse = model.AdminResponse;
            complaint.ResponseDate = DateTime.Now;

            _context.Entry(complaint).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            var complaintDto = new ComplaintDto
            {
                Id = complaint.Id,
                UserId = complaint.UserId,
                ProductId = complaint.ProductId,
                Username = complaint.User.Username,
                Subject = complaint.Subject,
                Description = complaint.Description,
                CreatedAt = complaint.CreatedAt,
                Status = complaint.Status,
                AdminResponse = complaint.AdminResponse,
                ResponseDate = complaint.ResponseDate
            };

            return Ok(ApiResponse<ComplaintDto>.SuccessResponse(complaintDto, "Réclamation mise à jour avec succès"));
        }
    }
}
