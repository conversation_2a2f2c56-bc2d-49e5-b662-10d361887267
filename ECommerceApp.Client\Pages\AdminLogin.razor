@page "/admin-login"
@inject AuthService AuthService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Connexion Administrateur - E-Shop</PageTitle>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="oi oi-lock-locked display-1 text-danger mb-3"></i>
                        <h2 class="fw-bold">Connexion Administrateur</h2>
                        <p class="text-muted">Accès réservé aux administrateurs</p>
                    </div>

                    <EditForm Model="model" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="text-danger mb-3" />

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="oi oi-warning me-2"></i> @errorMessage
                                <button type="button" class="btn-close" @onclick="() => errorMessage = string.Empty"></button>
                            </div>
                        }

                        <div class="form-floating mb-3">
                            <InputText @bind-Value="model.Email" class="form-control" id="email" placeholder="Email" />
                            <label for="email">Email</label>
                            <ValidationMessage For="@(() => model.Email)" class="text-danger" />
                        </div>

                        <div class="form-floating mb-4">
                            <InputText @bind-Value="model.Password" class="form-control" id="password" type="password" placeholder="Mot de passe" />
                            <label for="password">Mot de passe</label>
                            <ValidationMessage For="@(() => model.Password)" class="text-danger" />
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger btn-lg" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    <span>Connexion en cours...</span>
                                }
                                else
                                {
                                    <i class="oi oi-account-login me-2"></i>
                                    <span>Se connecter</span>
                                }
                            </button>
                            <button type="button" class="btn btn-outline-secondary" @onclick="NavigateToLanding">
                                <i class="oi oi-arrow-left me-2"></i> Retour à l'accueil
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private UserLoginDto model = new UserLoginDto
    {
        Email = "<EMAIL>",
        Password = "admin123"
    };
    private bool isLoading = false;
    private string errorMessage = string.Empty;

    private async Task HandleLogin()
    {
        Console.WriteLine("HandleAdminLogin called");
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            Console.WriteLine("Calling AuthService.Login for admin");
            var response = await AuthService.Login(model);
            Console.WriteLine("Login response received: " + response.Success);

            if (response.Success)
            {
                Console.WriteLine("Login successful, checking if user is admin");

                if (AuthService.IsAdmin)
                {
                    Console.WriteLine("User is admin, redirecting to admin dashboard");
                    await JSRuntime.InvokeVoidAsync("console.log", "Admin login successful, redirecting to admin dashboard");

                    await Task.Delay(500);

                    NavigationManager.NavigateTo("/admin", true);
                }
                else
                {
                    Console.WriteLine("User is not admin, showing error");
                    errorMessage = "Vous n'avez pas les droits d'administrateur.";
                    await AuthService.Logout();
                }
            }
            else
            {
                Console.WriteLine("Login failed: " + response.Message);
                errorMessage = response.Message;
                if (response.Errors != null && response.Errors.Any())
                {
                    errorMessage = string.Join(", ", response.Errors);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Login exception: " + ex.Message);
            errorMessage = $"Une erreur s'est produite: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void NavigateToLanding()
    {
        NavigationManager.NavigateTo("/");
    }
}
